using UnityEngine;

namespace GameBase
{
    public class GameNet : MonoBehaviour
    {
        private static NetModule _net;

        private static HttpModule _http;

        public static NetModule Net
        {
            get
            {
                if (_net == null)
                {
                    var obj = GameObject.Find("Network");
                    if (obj == null)
                    {
                        obj = new GameObject("Network");
                    }
                    DontDestroyOnLoad(obj);
                    _net = obj.AddComponent<NetModule>();
                }
                return _net;
            }
            private set { }
        }

        public static HttpModule Http
        {
            get
            {
                if (_http == null)
                {
                    var obj = GameObject.Find("Http");
                    if (obj == null)
                    {
                        obj = new GameObject("Http");
                    }
                    DontDestroyOnLoad(obj);
                    _http = obj.AddComponent<HttpModule>();
                }
                return _http;
            }
            private set { }
        }
    }
}