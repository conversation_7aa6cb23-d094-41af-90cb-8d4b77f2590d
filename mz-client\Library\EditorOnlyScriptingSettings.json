{"m_DefineSymbols": {"m_Value": {"Android": "UNITY_POST_PROCESSING_STACK_V2;TextMeshPro;ODIN_VALIDATOR;ODIN_VALIDATOR_3_1;ODIN_INSPECTOR;ODIN_INSPECTOR_3;ODIN_INSPECTOR_3_1;MAGICACLOTH2;UNITY_PIPELINE_URP;DOTWEEN;ENABLE_LOG;LiveScriptReload_Enabled;LiveScriptReload_IncludeInBuild_Enabled", "EmbeddedLinux": "UNITY_POST_PROCESSING_STACK_V2;TextMeshPro;DOTWEEN", "GameCoreScarlett": "DOTWEEN", "GameCoreXboxOne": "UNITY_POST_PROCESSING_STACK_V2;TextMeshPro;DOTWEEN", "LinuxHeadlessSimulation": "UNITY_POST_PROCESSING_STACK_V2;DOTWEEN", "Nintendo Switch": "UNITY_POST_PROCESSING_STACK_V2;TextMeshPro;DOTWEEN", "PS4": "UNITY_POST_PROCESSING_STACK_V2;TextMeshPro;DOTWEEN", "PS5": "UNITY_POST_PROCESSING_STACK_V2;TextMeshPro;DOTWEEN", "QNX": "UNITY_POST_PROCESSING_STACK_V2;TextMeshPro;DOTWEEN", "Stadia": "UNITY_POST_PROCESSING_STACK_V2;TextMeshPro;DOTWEEN", "Standalone": "UNITY_POST_PROCESSING_STACK_V2;ODIN_VALIDATOR;ODIN_VALIDATOR_3_1;ODIN_INSPECTOR;ODIN_INSPECTOR_3;ODIN_INSPECTOR_3_1;MAGICACLOTH2;TextMeshPro;DOTWEEN;UNITY_PIPELINE_URP;LiveScriptReload_Enabled;LiveScriptReload_IncludeInBuild_Enabled", "VisionOS": "UNITY_POST_PROCESSING_STACK_V2;TextMeshPro;DOTWEEN", "WebGL": "UNITY_POST_PROCESSING_STACK_V2;TextMeshPro;DOTWEEN", "Windows Store Apps": "UNITY_POST_PROCESSING_STACK_V2;DOTWEEN", "XboxOne": "UNITY_POST_PROCESSING_STACK_V2;TextMeshPro;DOTWEEN", "iPhone": "UNITY_POST_PROCESSING_STACK_V2;TextMeshPro;DOTWEEN", "tvOS": "UNITY_POST_PROCESSING_STACK_V2;TextMeshPro;DOTWEEN"}, "m_Initialized": true}, "m_AllowUnsafeCode": {"m_Value": false, "m_Initialized": false}, "m_ScriptDebugInfoEnabled": {"m_Value": true, "m_Initialized": true}}