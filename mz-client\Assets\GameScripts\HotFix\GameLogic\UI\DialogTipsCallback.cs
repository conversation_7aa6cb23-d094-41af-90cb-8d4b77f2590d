using UnityEngine;
using UnityEngine.UI;
using TEngine;

namespace GameLogic
{
    [Window(UILayer.UI)]
    class DialogTipsCallback : UIWindow
    {
        public delegate void OnCompleteDelegate();
        public OnCompleteDelegate OnComplete;

        #region 脚本工具生成的代码
        private Text m_textTips;
        private Button m_btnOk;
        protected override void ScriptGenerator()
        {
            m_textTips = FindChildComponent<Text>("Title/m_textTips");
            m_btnOk = FindChildComponent<Button>("m_btnOk");
            m_btnOk.onClick.AddListener(OnClickOkBtn);

            m_textTips.text = this.UserDatas[0] as string;
            OnComplete = this.UserDatas[1] as OnCompleteDelegate;
        }
        #endregion

        #region 事件
        private void OnClickOkBtn()
        {
            OnComplete.Invoke();
        }
        #endregion

    }
}
