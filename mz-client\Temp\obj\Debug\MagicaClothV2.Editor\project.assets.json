{"version": 3, "targets": {".NETStandard,Version=v2.1": {"MagicaClothV2/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "compile": {"bin/placeholder/MagicaClothV2.dll": {}}, "runtime": {"bin/placeholder/MagicaClothV2.dll": {}}}}}, "libraries": {"MagicaClothV2/1.0.0": {"type": "project", "path": "MagicaClothV2.csproj", "msbuildProject": "MagicaClothV2.csproj"}}, "projectFileDependencyGroups": {".NETStandard,Version=v2.1": ["MagicaClothV2 >= 1.0.0"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Work\\military-zone-client\\mz-client\\MagicaClothV2.Editor.csproj", "projectName": "MagicaClothV2.Editor", "projectPath": "D:\\Work\\military-zone-client\\mz-client\\MagicaClothV2.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Work\\military-zone-client\\mz-client\\Temp\\obj\\Debug\\MagicaClothV2.Editor\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Work\\military-zone-client\\mz-client\\MagicaClothV2.csproj": {"projectPath": "D:\\Work\\military-zone-client\\mz-client\\MagicaClothV2.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.104\\RuntimeIdentifierGraph.json"}}}}