{"version": 3, "targets": {".NETStandard,Version=v2.1": {"FastScriptReload.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"FastScriptReload.Runtime": "1.0.0"}, "compile": {"bin/placeholder/FastScriptReload.Editor.dll": {}}, "runtime": {"bin/placeholder/FastScriptReload.Editor.dll": {}}}, "FastScriptReload.Runtime/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "compile": {"bin/placeholder/FastScriptReload.Runtime.dll": {}}, "runtime": {"bin/placeholder/FastScriptReload.Runtime.dll": {}}}, "LiteNetLib/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "compile": {"bin/placeholder/LiteNetLib.dll": {}}, "runtime": {"bin/placeholder/LiteNetLib.dll": {}}}, "LiveScriptReload.Runtime/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"FastScriptReload.Runtime": "1.0.0", "LiteNetLib": "1.0.0"}, "compile": {"bin/placeholder/LiveScriptReload.Runtime.dll": {}}, "runtime": {"bin/placeholder/LiveScriptReload.Runtime.dll": {}}}}}, "libraries": {"FastScriptReload.Editor/1.0.0": {"type": "project", "path": "FastScriptReload.Editor.csproj", "msbuildProject": "FastScriptReload.Editor.csproj"}, "FastScriptReload.Runtime/1.0.0": {"type": "project", "path": "FastScriptReload.Runtime.csproj", "msbuildProject": "FastScriptReload.Runtime.csproj"}, "LiteNetLib/1.0.0": {"type": "project", "path": "LiteNetLib.csproj", "msbuildProject": "LiteNetLib.csproj"}, "LiveScriptReload.Runtime/1.0.0": {"type": "project", "path": "LiveScriptReload.Runtime.csproj", "msbuildProject": "LiveScriptReload.Runtime.csproj"}}, "projectFileDependencyGroups": {".NETStandard,Version=v2.1": ["FastScriptReload.Editor >= 1.0.0", "FastScriptReload.Runtime >= 1.0.0", "LiteNetLib >= 1.0.0", "LiveScriptReload.Runtime >= 1.0.0"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Work\\military-zone-client\\mz-client\\LiveScriptReload.Editor.csproj", "projectName": "LiveScriptReload.Editor", "projectPath": "D:\\Work\\military-zone-client\\mz-client\\LiveScriptReload.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Work\\military-zone-client\\mz-client\\Temp\\obj\\Debug\\LiveScriptReload.Editor\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Work\\military-zone-client\\mz-client\\FastScriptReload.Editor.csproj": {"projectPath": "D:\\Work\\military-zone-client\\mz-client\\FastScriptReload.Editor.csproj"}, "D:\\Work\\military-zone-client\\mz-client\\FastScriptReload.Runtime.csproj": {"projectPath": "D:\\Work\\military-zone-client\\mz-client\\FastScriptReload.Runtime.csproj"}, "D:\\Work\\military-zone-client\\mz-client\\LiteNetLib.csproj": {"projectPath": "D:\\Work\\military-zone-client\\mz-client\\LiteNetLib.csproj"}, "D:\\Work\\military-zone-client\\mz-client\\LiveScriptReload.Runtime.csproj": {"projectPath": "D:\\Work\\military-zone-client\\mz-client\\LiveScriptReload.Runtime.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.104\\RuntimeIdentifierGraph.json"}}}}