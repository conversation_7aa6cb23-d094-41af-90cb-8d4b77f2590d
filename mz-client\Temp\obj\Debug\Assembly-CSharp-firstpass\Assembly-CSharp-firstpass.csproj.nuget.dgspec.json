{"format": 1, "restore": {"D:\\Work\\military-zone-client\\mz-client\\Assembly-CSharp-firstpass.csproj": {}}, "projects": {"D:\\Work\\military-zone-client\\mz-client\\Assembly-CSharp-firstpass.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Work\\military-zone-client\\mz-client\\Assembly-CSharp-firstpass.csproj", "projectName": "Assembly-CSharp-firstpass", "projectPath": "D:\\Work\\military-zone-client\\mz-client\\Assembly-CSharp-firstpass.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Work\\military-zone-client\\mz-client\\Temp\\obj\\Debug\\Assembly-CSharp-firstpass\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Work\\military-zone-client\\mz-client\\DG.Tweening.csproj": {"projectPath": "D:\\Work\\military-zone-client\\mz-client\\DG.Tweening.csproj"}, "D:\\Work\\military-zone-client\\mz-client\\DOTweenPro-Exclude.csproj": {"projectPath": "D:\\Work\\military-zone-client\\mz-client\\DOTweenPro-Exclude.csproj"}, "D:\\Work\\military-zone-client\\mz-client\\EPO.csproj": {"projectPath": "D:\\Work\\military-zone-client\\mz-client\\EPO.csproj"}, "D:\\Work\\military-zone-client\\mz-client\\EPODemo.csproj": {"projectPath": "D:\\Work\\military-zone-client\\mz-client\\EPODemo.csproj"}, "D:\\Work\\military-zone-client\\mz-client\\EPOEditor.csproj": {"projectPath": "D:\\Work\\military-zone-client\\mz-client\\EPOEditor.csproj"}, "D:\\Work\\military-zone-client\\mz-client\\EPOHDRP.csproj": {"projectPath": "D:\\Work\\military-zone-client\\mz-client\\EPOHDRP.csproj"}, "D:\\Work\\military-zone-client\\mz-client\\EPOURP.csproj": {"projectPath": "D:\\Work\\military-zone-client\\mz-client\\EPOURP.csproj"}, "D:\\Work\\military-zone-client\\mz-client\\EPOUtilities.csproj": {"projectPath": "D:\\Work\\military-zone-client\\mz-client\\EPOUtilities.csproj"}, "D:\\Work\\military-zone-client\\mz-client\\FR2.csproj": {"projectPath": "D:\\Work\\military-zone-client\\mz-client\\FR2.csproj"}, "D:\\Work\\military-zone-client\\mz-client\\GameBase.csproj": {"projectPath": "D:\\Work\\military-zone-client\\mz-client\\GameBase.csproj"}, "D:\\Work\\military-zone-client\\mz-client\\GameCommon.csproj": {"projectPath": "D:\\Work\\military-zone-client\\mz-client\\GameCommon.csproj"}, "D:\\Work\\military-zone-client\\mz-client\\GameLogic.csproj": {"projectPath": "D:\\Work\\military-zone-client\\mz-client\\GameLogic.csproj"}, "D:\\Work\\military-zone-client\\mz-client\\GameProto.csproj": {"projectPath": "D:\\Work\\military-zone-client\\mz-client\\GameProto.csproj"}, "D:\\Work\\military-zone-client\\mz-client\\JUTPS.csproj": {"projectPath": "D:\\Work\\military-zone-client\\mz-client\\JUTPS.csproj"}, "D:\\Work\\military-zone-client\\mz-client\\JUTPS.CustomEditors.csproj": {"projectPath": "D:\\Work\\military-zone-client\\mz-client\\JUTPS.CustomEditors.csproj"}, "D:\\Work\\military-zone-client\\mz-client\\LiteNetLib.csproj": {"projectPath": "D:\\Work\\military-zone-client\\mz-client\\LiteNetLib.csproj"}, "D:\\Work\\military-zone-client\\mz-client\\LiveScriptReload.Editor.csproj": {"projectPath": "D:\\Work\\military-zone-client\\mz-client\\LiveScriptReload.Editor.csproj"}, "D:\\Work\\military-zone-client\\mz-client\\LiveScriptReload.Runtime.csproj": {"projectPath": "D:\\Work\\military-zone-client\\mz-client\\LiveScriptReload.Runtime.csproj"}, "D:\\Work\\military-zone-client\\mz-client\\MagicaClothV2.csproj": {"projectPath": "D:\\Work\\military-zone-client\\mz-client\\MagicaClothV2.csproj"}, "D:\\Work\\military-zone-client\\mz-client\\MagicaClothV2.Editor.csproj": {"projectPath": "D:\\Work\\military-zone-client\\mz-client\\MagicaClothV2.Editor.csproj"}, "D:\\Work\\military-zone-client\\mz-client\\PhysicsTankMaker-Editor-Exclude.csproj": {"projectPath": "D:\\Work\\military-zone-client\\mz-client\\PhysicsTankMaker-Editor-Exclude.csproj"}, "D:\\Work\\military-zone-client\\mz-client\\PhysicsTankMaker.csproj": {"projectPath": "D:\\Work\\military-zone-client\\mz-client\\PhysicsTankMaker.csproj"}, "D:\\Work\\military-zone-client\\mz-client\\Physics_Track_System-exclude.csproj": {"projectPath": "D:\\Work\\military-zone-client\\mz-client\\Physics_Track_System-exclude.csproj"}, "D:\\Work\\military-zone-client\\mz-client\\TEngine.Editor.csproj": {"projectPath": "D:\\Work\\military-zone-client\\mz-client\\TEngine.Editor.csproj"}, "D:\\Work\\military-zone-client\\mz-client\\TEngine.Runtime.csproj": {"projectPath": "D:\\Work\\military-zone-client\\mz-client\\TEngine.Runtime.csproj"}, "D:\\Work\\military-zone-client\\mz-client\\UniTask.Addressables.csproj": {"projectPath": "D:\\Work\\military-zone-client\\mz-client\\UniTask.Addressables.csproj"}, "D:\\Work\\military-zone-client\\mz-client\\UniTask.csproj": {"projectPath": "D:\\Work\\military-zone-client\\mz-client\\UniTask.csproj"}, "D:\\Work\\military-zone-client\\mz-client\\UniTask.DOTween.csproj": {"projectPath": "D:\\Work\\military-zone-client\\mz-client\\UniTask.DOTween.csproj"}, "D:\\Work\\military-zone-client\\mz-client\\UniTask.Linq.csproj": {"projectPath": "D:\\Work\\military-zone-client\\mz-client\\UniTask.Linq.csproj"}, "D:\\Work\\military-zone-client\\mz-client\\UniTask.TextMeshPro.csproj": {"projectPath": "D:\\Work\\military-zone-client\\mz-client\\UniTask.TextMeshPro.csproj"}, "D:\\Work\\military-zone-client\\mz-client\\YooAsset.csproj": {"projectPath": "D:\\Work\\military-zone-client\\mz-client\\YooAsset.csproj"}, "D:\\Work\\military-zone-client\\mz-client\\YooAsset.Editor.csproj": {"projectPath": "D:\\Work\\military-zone-client\\mz-client\\YooAsset.Editor.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.104\\RuntimeIdentifierGraph.json"}}}, "D:\\Work\\military-zone-client\\mz-client\\DG.Tweening.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Work\\military-zone-client\\mz-client\\DG.Tweening.csproj", "projectName": "<PERSON><PERSON><PERSON>T<PERSON>", "projectPath": "D:\\Work\\military-zone-client\\mz-client\\DG.Tweening.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Work\\military-zone-client\\mz-client\\Temp\\obj\\Debug\\DG.Tweening\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.104\\RuntimeIdentifierGraph.json"}}}, "D:\\Work\\military-zone-client\\mz-client\\DOTweenPro-Exclude.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Work\\military-zone-client\\mz-client\\DOTweenPro-Exclude.csproj", "projectName": "DOTweenPro-Exclude", "projectPath": "D:\\Work\\military-zone-client\\mz-client\\DOTweenPro-Exclude.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Work\\military-zone-client\\mz-client\\Temp\\obj\\Debug\\DOTweenPro-Exclude\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Work\\military-zone-client\\mz-client\\DG.Tweening.csproj": {"projectPath": "D:\\Work\\military-zone-client\\mz-client\\DG.Tweening.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.104\\RuntimeIdentifierGraph.json"}}}, "D:\\Work\\military-zone-client\\mz-client\\EPO.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Work\\military-zone-client\\mz-client\\EPO.csproj", "projectName": "EPO", "projectPath": "D:\\Work\\military-zone-client\\mz-client\\EPO.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Work\\military-zone-client\\mz-client\\Temp\\obj\\Debug\\EPO\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Work\\military-zone-client\\mz-client\\EPOUtilities.csproj": {"projectPath": "D:\\Work\\military-zone-client\\mz-client\\EPOUtilities.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.104\\RuntimeIdentifierGraph.json"}}}, "D:\\Work\\military-zone-client\\mz-client\\EPODemo.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Work\\military-zone-client\\mz-client\\EPODemo.csproj", "projectName": "EPODemo", "projectPath": "D:\\Work\\military-zone-client\\mz-client\\EPODemo.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Work\\military-zone-client\\mz-client\\Temp\\obj\\Debug\\EPODemo\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Work\\military-zone-client\\mz-client\\EPO.csproj": {"projectPath": "D:\\Work\\military-zone-client\\mz-client\\EPO.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.104\\RuntimeIdentifierGraph.json"}}}, "D:\\Work\\military-zone-client\\mz-client\\EPOEditor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Work\\military-zone-client\\mz-client\\EPOEditor.csproj", "projectName": "EPOEditor", "projectPath": "D:\\Work\\military-zone-client\\mz-client\\EPOEditor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Work\\military-zone-client\\mz-client\\Temp\\obj\\Debug\\EPOEditor\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Work\\military-zone-client\\mz-client\\EPO.csproj": {"projectPath": "D:\\Work\\military-zone-client\\mz-client\\EPO.csproj"}, "D:\\Work\\military-zone-client\\mz-client\\EPOHDRP.csproj": {"projectPath": "D:\\Work\\military-zone-client\\mz-client\\EPOHDRP.csproj"}, "D:\\Work\\military-zone-client\\mz-client\\EPOURP.csproj": {"projectPath": "D:\\Work\\military-zone-client\\mz-client\\EPOURP.csproj"}, "D:\\Work\\military-zone-client\\mz-client\\EPOUtilities.csproj": {"projectPath": "D:\\Work\\military-zone-client\\mz-client\\EPOUtilities.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.104\\RuntimeIdentifierGraph.json"}}}, "D:\\Work\\military-zone-client\\mz-client\\EPOHDRP.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Work\\military-zone-client\\mz-client\\EPOHDRP.csproj", "projectName": "EPOHDRP", "projectPath": "D:\\Work\\military-zone-client\\mz-client\\EPOHDRP.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Work\\military-zone-client\\mz-client\\Temp\\obj\\Debug\\EPOHDRP\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Work\\military-zone-client\\mz-client\\EPO.csproj": {"projectPath": "D:\\Work\\military-zone-client\\mz-client\\EPO.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.104\\RuntimeIdentifierGraph.json"}}}, "D:\\Work\\military-zone-client\\mz-client\\EPOURP.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Work\\military-zone-client\\mz-client\\EPOURP.csproj", "projectName": "EPOURP", "projectPath": "D:\\Work\\military-zone-client\\mz-client\\EPOURP.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Work\\military-zone-client\\mz-client\\Temp\\obj\\Debug\\EPOURP\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Work\\military-zone-client\\mz-client\\EPO.csproj": {"projectPath": "D:\\Work\\military-zone-client\\mz-client\\EPO.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.104\\RuntimeIdentifierGraph.json"}}}, "D:\\Work\\military-zone-client\\mz-client\\EPOUtilities.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Work\\military-zone-client\\mz-client\\EPOUtilities.csproj", "projectName": "EPOUtilities", "projectPath": "D:\\Work\\military-zone-client\\mz-client\\EPOUtilities.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Work\\military-zone-client\\mz-client\\Temp\\obj\\Debug\\EPOUtilities\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.104\\RuntimeIdentifierGraph.json"}}}, "D:\\Work\\military-zone-client\\mz-client\\FastScriptReload.Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Work\\military-zone-client\\mz-client\\FastScriptReload.Editor.csproj", "projectName": "FastScriptReload.Editor", "projectPath": "D:\\Work\\military-zone-client\\mz-client\\FastScriptReload.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Work\\military-zone-client\\mz-client\\Temp\\obj\\Debug\\FastScriptReload.Editor\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Work\\military-zone-client\\mz-client\\FastScriptReload.Runtime.csproj": {"projectPath": "D:\\Work\\military-zone-client\\mz-client\\FastScriptReload.Runtime.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.104\\RuntimeIdentifierGraph.json"}}}, "D:\\Work\\military-zone-client\\mz-client\\FastScriptReload.Runtime.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Work\\military-zone-client\\mz-client\\FastScriptReload.Runtime.csproj", "projectName": "FastScriptReload.Runtime", "projectPath": "D:\\Work\\military-zone-client\\mz-client\\FastScriptReload.Runtime.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Work\\military-zone-client\\mz-client\\Temp\\obj\\Debug\\FastScriptReload.Runtime\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.104\\RuntimeIdentifierGraph.json"}}}, "D:\\Work\\military-zone-client\\mz-client\\FR2.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Work\\military-zone-client\\mz-client\\FR2.csproj", "projectName": "FR2", "projectPath": "D:\\Work\\military-zone-client\\mz-client\\FR2.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Work\\military-zone-client\\mz-client\\Temp\\obj\\Debug\\FR2\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.104\\RuntimeIdentifierGraph.json"}}}, "D:\\Work\\military-zone-client\\mz-client\\GameBase.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Work\\military-zone-client\\mz-client\\GameBase.csproj", "projectName": "GameBase", "projectPath": "D:\\Work\\military-zone-client\\mz-client\\GameBase.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Work\\military-zone-client\\mz-client\\Temp\\obj\\Debug\\GameBase\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Work\\military-zone-client\\mz-client\\GameProto.csproj": {"projectPath": "D:\\Work\\military-zone-client\\mz-client\\GameProto.csproj"}, "D:\\Work\\military-zone-client\\mz-client\\TEngine.Runtime.csproj": {"projectPath": "D:\\Work\\military-zone-client\\mz-client\\TEngine.Runtime.csproj"}, "D:\\Work\\military-zone-client\\mz-client\\UniTask.csproj": {"projectPath": "D:\\Work\\military-zone-client\\mz-client\\UniTask.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.104\\RuntimeIdentifierGraph.json"}}}, "D:\\Work\\military-zone-client\\mz-client\\GameCommon.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Work\\military-zone-client\\mz-client\\GameCommon.csproj", "projectName": "GameCommon", "projectPath": "D:\\Work\\military-zone-client\\mz-client\\GameCommon.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Work\\military-zone-client\\mz-client\\Temp\\obj\\Debug\\GameCommon\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Work\\military-zone-client\\mz-client\\GameBase.csproj": {"projectPath": "D:\\Work\\military-zone-client\\mz-client\\GameBase.csproj"}, "D:\\Work\\military-zone-client\\mz-client\\GameProto.csproj": {"projectPath": "D:\\Work\\military-zone-client\\mz-client\\GameProto.csproj"}, "D:\\Work\\military-zone-client\\mz-client\\TEngine.Runtime.csproj": {"projectPath": "D:\\Work\\military-zone-client\\mz-client\\TEngine.Runtime.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.104\\RuntimeIdentifierGraph.json"}}}, "D:\\Work\\military-zone-client\\mz-client\\GameLogic.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Work\\military-zone-client\\mz-client\\GameLogic.csproj", "projectName": "GameLogic", "projectPath": "D:\\Work\\military-zone-client\\mz-client\\GameLogic.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Work\\military-zone-client\\mz-client\\Temp\\obj\\Debug\\GameLogic\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Work\\military-zone-client\\mz-client\\DG.Tweening.csproj": {"projectPath": "D:\\Work\\military-zone-client\\mz-client\\DG.Tweening.csproj"}, "D:\\Work\\military-zone-client\\mz-client\\GameBase.csproj": {"projectPath": "D:\\Work\\military-zone-client\\mz-client\\GameBase.csproj"}, "D:\\Work\\military-zone-client\\mz-client\\GameCommon.csproj": {"projectPath": "D:\\Work\\military-zone-client\\mz-client\\GameCommon.csproj"}, "D:\\Work\\military-zone-client\\mz-client\\GameProto.csproj": {"projectPath": "D:\\Work\\military-zone-client\\mz-client\\GameProto.csproj"}, "D:\\Work\\military-zone-client\\mz-client\\JUTPS.csproj": {"projectPath": "D:\\Work\\military-zone-client\\mz-client\\JUTPS.csproj"}, "D:\\Work\\military-zone-client\\mz-client\\PhysicsTankMaker.csproj": {"projectPath": "D:\\Work\\military-zone-client\\mz-client\\PhysicsTankMaker.csproj"}, "D:\\Work\\military-zone-client\\mz-client\\TEngine.Runtime.csproj": {"projectPath": "D:\\Work\\military-zone-client\\mz-client\\TEngine.Runtime.csproj"}, "D:\\Work\\military-zone-client\\mz-client\\UniTask.csproj": {"projectPath": "D:\\Work\\military-zone-client\\mz-client\\UniTask.csproj"}, "D:\\Work\\military-zone-client\\mz-client\\YooAsset.csproj": {"projectPath": "D:\\Work\\military-zone-client\\mz-client\\YooAsset.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.104\\RuntimeIdentifierGraph.json"}}}, "D:\\Work\\military-zone-client\\mz-client\\GameProto.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Work\\military-zone-client\\mz-client\\GameProto.csproj", "projectName": "GameProto", "projectPath": "D:\\Work\\military-zone-client\\mz-client\\GameProto.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Work\\military-zone-client\\mz-client\\Temp\\obj\\Debug\\GameProto\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.104\\RuntimeIdentifierGraph.json"}}}, "D:\\Work\\military-zone-client\\mz-client\\JUTPS.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Work\\military-zone-client\\mz-client\\JUTPS.csproj", "projectName": "JUTPS", "projectPath": "D:\\Work\\military-zone-client\\mz-client\\JUTPS.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Work\\military-zone-client\\mz-client\\Temp\\obj\\Debug\\JUTPS\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Work\\military-zone-client\\mz-client\\GameCommon.csproj": {"projectPath": "D:\\Work\\military-zone-client\\mz-client\\GameCommon.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.104\\RuntimeIdentifierGraph.json"}}}, "D:\\Work\\military-zone-client\\mz-client\\JUTPS.CustomEditors.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Work\\military-zone-client\\mz-client\\JUTPS.CustomEditors.csproj", "projectName": "JUTPS.CustomEditors", "projectPath": "D:\\Work\\military-zone-client\\mz-client\\JUTPS.CustomEditors.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Work\\military-zone-client\\mz-client\\Temp\\obj\\Debug\\JUTPS.CustomEditors\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Work\\military-zone-client\\mz-client\\JUTPS.csproj": {"projectPath": "D:\\Work\\military-zone-client\\mz-client\\JUTPS.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.104\\RuntimeIdentifierGraph.json"}}}, "D:\\Work\\military-zone-client\\mz-client\\LiteNetLib.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Work\\military-zone-client\\mz-client\\LiteNetLib.csproj", "projectName": "LiteNetLib", "projectPath": "D:\\Work\\military-zone-client\\mz-client\\LiteNetLib.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Work\\military-zone-client\\mz-client\\Temp\\obj\\Debug\\LiteNetLib\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.104\\RuntimeIdentifierGraph.json"}}}, "D:\\Work\\military-zone-client\\mz-client\\LiveScriptReload.Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Work\\military-zone-client\\mz-client\\LiveScriptReload.Editor.csproj", "projectName": "LiveScriptReload.Editor", "projectPath": "D:\\Work\\military-zone-client\\mz-client\\LiveScriptReload.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Work\\military-zone-client\\mz-client\\Temp\\obj\\Debug\\LiveScriptReload.Editor\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Work\\military-zone-client\\mz-client\\FastScriptReload.Editor.csproj": {"projectPath": "D:\\Work\\military-zone-client\\mz-client\\FastScriptReload.Editor.csproj"}, "D:\\Work\\military-zone-client\\mz-client\\FastScriptReload.Runtime.csproj": {"projectPath": "D:\\Work\\military-zone-client\\mz-client\\FastScriptReload.Runtime.csproj"}, "D:\\Work\\military-zone-client\\mz-client\\LiteNetLib.csproj": {"projectPath": "D:\\Work\\military-zone-client\\mz-client\\LiteNetLib.csproj"}, "D:\\Work\\military-zone-client\\mz-client\\LiveScriptReload.Runtime.csproj": {"projectPath": "D:\\Work\\military-zone-client\\mz-client\\LiveScriptReload.Runtime.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.104\\RuntimeIdentifierGraph.json"}}}, "D:\\Work\\military-zone-client\\mz-client\\LiveScriptReload.Runtime.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Work\\military-zone-client\\mz-client\\LiveScriptReload.Runtime.csproj", "projectName": "LiveScriptReload.Runtime", "projectPath": "D:\\Work\\military-zone-client\\mz-client\\LiveScriptReload.Runtime.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Work\\military-zone-client\\mz-client\\Temp\\obj\\Debug\\LiveScriptReload.Runtime\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Work\\military-zone-client\\mz-client\\FastScriptReload.Runtime.csproj": {"projectPath": "D:\\Work\\military-zone-client\\mz-client\\FastScriptReload.Runtime.csproj"}, "D:\\Work\\military-zone-client\\mz-client\\LiteNetLib.csproj": {"projectPath": "D:\\Work\\military-zone-client\\mz-client\\LiteNetLib.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.104\\RuntimeIdentifierGraph.json"}}}, "D:\\Work\\military-zone-client\\mz-client\\MagicaClothV2.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Work\\military-zone-client\\mz-client\\MagicaClothV2.csproj", "projectName": "MagicaClothV2", "projectPath": "D:\\Work\\military-zone-client\\mz-client\\MagicaClothV2.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Work\\military-zone-client\\mz-client\\Temp\\obj\\Debug\\MagicaClothV2\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.104\\RuntimeIdentifierGraph.json"}}}, "D:\\Work\\military-zone-client\\mz-client\\MagicaClothV2.Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Work\\military-zone-client\\mz-client\\MagicaClothV2.Editor.csproj", "projectName": "MagicaClothV2.Editor", "projectPath": "D:\\Work\\military-zone-client\\mz-client\\MagicaClothV2.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Work\\military-zone-client\\mz-client\\Temp\\obj\\Debug\\MagicaClothV2.Editor\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Work\\military-zone-client\\mz-client\\MagicaClothV2.csproj": {"projectPath": "D:\\Work\\military-zone-client\\mz-client\\MagicaClothV2.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.104\\RuntimeIdentifierGraph.json"}}}, "D:\\Work\\military-zone-client\\mz-client\\PhysicsTankMaker-Editor-Exclude.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Work\\military-zone-client\\mz-client\\PhysicsTankMaker-Editor-Exclude.csproj", "projectName": "PhysicsTankMaker-Editor-Exclude", "projectPath": "D:\\Work\\military-zone-client\\mz-client\\PhysicsTankMaker-Editor-Exclude.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Work\\military-zone-client\\mz-client\\Temp\\obj\\Debug\\PhysicsTankMaker-Editor-Exclude\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Work\\military-zone-client\\mz-client\\GameCommon.csproj": {"projectPath": "D:\\Work\\military-zone-client\\mz-client\\GameCommon.csproj"}, "D:\\Work\\military-zone-client\\mz-client\\PhysicsTankMaker.csproj": {"projectPath": "D:\\Work\\military-zone-client\\mz-client\\PhysicsTankMaker.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.104\\RuntimeIdentifierGraph.json"}}}, "D:\\Work\\military-zone-client\\mz-client\\PhysicsTankMaker.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Work\\military-zone-client\\mz-client\\PhysicsTankMaker.csproj", "projectName": "PhysicsTankMaker", "projectPath": "D:\\Work\\military-zone-client\\mz-client\\PhysicsTankMaker.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Work\\military-zone-client\\mz-client\\Temp\\obj\\Debug\\PhysicsTankMaker\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Work\\military-zone-client\\mz-client\\GameCommon.csproj": {"projectPath": "D:\\Work\\military-zone-client\\mz-client\\GameCommon.csproj"}, "D:\\Work\\military-zone-client\\mz-client\\TEngine.Runtime.csproj": {"projectPath": "D:\\Work\\military-zone-client\\mz-client\\TEngine.Runtime.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.104\\RuntimeIdentifierGraph.json"}}}, "D:\\Work\\military-zone-client\\mz-client\\Physics_Track_System-exclude.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Work\\military-zone-client\\mz-client\\Physics_Track_System-exclude.csproj", "projectName": "Physics_Track_System-exclude", "projectPath": "D:\\Work\\military-zone-client\\mz-client\\Physics_Track_System-exclude.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Work\\military-zone-client\\mz-client\\Temp\\obj\\Debug\\Physics_Track_System-exclude\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Work\\military-zone-client\\mz-client\\PhysicsTankMaker.csproj": {"projectPath": "D:\\Work\\military-zone-client\\mz-client\\PhysicsTankMaker.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.104\\RuntimeIdentifierGraph.json"}}}, "D:\\Work\\military-zone-client\\mz-client\\TEngine.Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Work\\military-zone-client\\mz-client\\TEngine.Editor.csproj", "projectName": "TEngine.Editor", "projectPath": "D:\\Work\\military-zone-client\\mz-client\\TEngine.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Work\\military-zone-client\\mz-client\\Temp\\obj\\Debug\\TEngine.Editor\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Work\\military-zone-client\\mz-client\\GameLogic.csproj": {"projectPath": "D:\\Work\\military-zone-client\\mz-client\\GameLogic.csproj"}, "D:\\Work\\military-zone-client\\mz-client\\TEngine.Runtime.csproj": {"projectPath": "D:\\Work\\military-zone-client\\mz-client\\TEngine.Runtime.csproj"}, "D:\\Work\\military-zone-client\\mz-client\\YooAsset.csproj": {"projectPath": "D:\\Work\\military-zone-client\\mz-client\\YooAsset.csproj"}, "D:\\Work\\military-zone-client\\mz-client\\YooAsset.Editor.csproj": {"projectPath": "D:\\Work\\military-zone-client\\mz-client\\YooAsset.Editor.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.104\\RuntimeIdentifierGraph.json"}}}, "D:\\Work\\military-zone-client\\mz-client\\TEngine.Runtime.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Work\\military-zone-client\\mz-client\\TEngine.Runtime.csproj", "projectName": "TEngine.Runtime", "projectPath": "D:\\Work\\military-zone-client\\mz-client\\TEngine.Runtime.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Work\\military-zone-client\\mz-client\\Temp\\obj\\Debug\\TEngine.Runtime\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Work\\military-zone-client\\mz-client\\UniTask.csproj": {"projectPath": "D:\\Work\\military-zone-client\\mz-client\\UniTask.csproj"}, "D:\\Work\\military-zone-client\\mz-client\\YooAsset.csproj": {"projectPath": "D:\\Work\\military-zone-client\\mz-client\\YooAsset.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.104\\RuntimeIdentifierGraph.json"}}}, "D:\\Work\\military-zone-client\\mz-client\\UniTask.Addressables.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Work\\military-zone-client\\mz-client\\UniTask.Addressables.csproj", "projectName": "UniTask.Addressables", "projectPath": "D:\\Work\\military-zone-client\\mz-client\\UniTask.Addressables.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Work\\military-zone-client\\mz-client\\Temp\\obj\\Debug\\UniTask.Addressables\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Work\\military-zone-client\\mz-client\\UniTask.csproj": {"projectPath": "D:\\Work\\military-zone-client\\mz-client\\UniTask.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.104\\RuntimeIdentifierGraph.json"}}}, "D:\\Work\\military-zone-client\\mz-client\\UniTask.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Work\\military-zone-client\\mz-client\\UniTask.csproj", "projectName": "UniTask", "projectPath": "D:\\Work\\military-zone-client\\mz-client\\UniTask.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Work\\military-zone-client\\mz-client\\Temp\\obj\\Debug\\UniTask\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Work\\military-zone-client\\mz-client\\YooAsset.csproj": {"projectPath": "D:\\Work\\military-zone-client\\mz-client\\YooAsset.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.104\\RuntimeIdentifierGraph.json"}}}, "D:\\Work\\military-zone-client\\mz-client\\UniTask.DOTween.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Work\\military-zone-client\\mz-client\\UniTask.DOTween.csproj", "projectName": "UniTask.DOTween", "projectPath": "D:\\Work\\military-zone-client\\mz-client\\UniTask.DOTween.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Work\\military-zone-client\\mz-client\\Temp\\obj\\Debug\\UniTask.DOTween\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Work\\military-zone-client\\mz-client\\UniTask.csproj": {"projectPath": "D:\\Work\\military-zone-client\\mz-client\\UniTask.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.104\\RuntimeIdentifierGraph.json"}}}, "D:\\Work\\military-zone-client\\mz-client\\UniTask.Linq.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Work\\military-zone-client\\mz-client\\UniTask.Linq.csproj", "projectName": "UniTask.Linq", "projectPath": "D:\\Work\\military-zone-client\\mz-client\\UniTask.Linq.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Work\\military-zone-client\\mz-client\\Temp\\obj\\Debug\\UniTask.Linq\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Work\\military-zone-client\\mz-client\\UniTask.csproj": {"projectPath": "D:\\Work\\military-zone-client\\mz-client\\UniTask.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.104\\RuntimeIdentifierGraph.json"}}}, "D:\\Work\\military-zone-client\\mz-client\\UniTask.TextMeshPro.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Work\\military-zone-client\\mz-client\\UniTask.TextMeshPro.csproj", "projectName": "UniTask.TextMeshPro", "projectPath": "D:\\Work\\military-zone-client\\mz-client\\UniTask.TextMeshPro.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Work\\military-zone-client\\mz-client\\Temp\\obj\\Debug\\UniTask.TextMeshPro\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Work\\military-zone-client\\mz-client\\UniTask.csproj": {"projectPath": "D:\\Work\\military-zone-client\\mz-client\\UniTask.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.104\\RuntimeIdentifierGraph.json"}}}, "D:\\Work\\military-zone-client\\mz-client\\YooAsset.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Work\\military-zone-client\\mz-client\\YooAsset.csproj", "projectName": "YooAsset", "projectPath": "D:\\Work\\military-zone-client\\mz-client\\YooAsset.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Work\\military-zone-client\\mz-client\\Temp\\obj\\Debug\\YooAsset\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.104\\RuntimeIdentifierGraph.json"}}}, "D:\\Work\\military-zone-client\\mz-client\\YooAsset.Editor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Work\\military-zone-client\\mz-client\\YooAsset.Editor.csproj", "projectName": "YooAsset.Editor", "projectPath": "D:\\Work\\military-zone-client\\mz-client\\YooAsset.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Work\\military-zone-client\\mz-client\\Temp\\obj\\Debug\\YooAsset.Editor\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Work\\military-zone-client\\mz-client\\YooAsset.csproj": {"projectPath": "D:\\Work\\military-zone-client\\mz-client\\YooAsset.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.104\\RuntimeIdentifierGraph.json"}}}}}