-target:library
-out:"Library/Bee/artifacts/1900b0aEDbg.dag/GameBase.dll"
-refout:"Library/Bee/artifacts/1900b0aEDbg.dag/GameBase.ref.dll"
-define:UNITY_2022_3_51
-define:UNITY_2022_3
-define:UNITY_2022
-define:UNITY_5_3_OR_NEWER
-define:UNITY_5_4_OR_NEWER
-define:UNITY_5_5_OR_NEWER
-define:UNITY_5_6_OR_NEWER
-define:UNITY_2017_1_OR_NEWER
-define:UNITY_2017_2_OR_NEWER
-define:UNITY_2017_3_OR_NEWER
-define:UNITY_2017_4_OR_NEWER
-define:UNITY_2018_1_OR_NEWER
-define:UNITY_2018_2_OR_NEWER
-define:UNITY_2018_3_OR_NEWER
-define:UNITY_2018_4_OR_NEWER
-define:UNITY_2019_1_OR_NEWER
-define:UNITY_2019_2_OR_NEWER
-define:UNITY_2019_3_OR_NEWER
-define:UNITY_2019_4_OR_NEWER
-define:UNITY_2020_1_OR_NEWER
-define:UNITY_2020_2_OR_NEWER
-define:UNITY_2020_3_OR_NEWER
-define:UNITY_2021_1_OR_NEWER
-define:UNITY_2021_2_OR_NEWER
-define:UNITY_2021_3_OR_NEWER
-define:UNITY_2022_1_OR_NEWER
-define:UNITY_2022_2_OR_NEWER
-define:UNITY_2022_3_OR_NEWER
-define:PLATFORM_ARCH_64
-define:UNITY_64
-define:UNITY_INCLUDE_TESTS
-define:ENABLE_AR
-define:ENABLE_AUDIO
-define:ENABLE_CACHING
-define:ENABLE_CLOTH
-define:ENABLE_EVENT_QUEUE
-define:ENABLE_MICROPHONE
-define:ENABLE_MULTIPLE_DISPLAYS
-define:ENABLE_PHYSICS
-define:ENABLE_TEXTURE_STREAMING
-define:ENABLE_VIRTUALTEXTURING
-define:ENABLE_LZMA
-define:ENABLE_UNITYEVENTS
-define:ENABLE_VR
-define:ENABLE_WEBCAM
-define:ENABLE_UNITYWEBREQUEST
-define:ENABLE_WWW
-define:ENABLE_CLOUD_SERVICES
-define:ENABLE_CLOUD_SERVICES_ADS
-define:ENABLE_CLOUD_SERVICES_USE_WEBREQUEST
-define:ENABLE_CLOUD_SERVICES_CRASH_REPORTING
-define:ENABLE_CLOUD_SERVICES_PURCHASING
-define:ENABLE_CLOUD_SERVICES_ANALYTICS
-define:ENABLE_CLOUD_SERVICES_BUILD
-define:ENABLE_EDITOR_GAME_SERVICES
-define:ENABLE_UNITY_GAME_SERVICES_ANALYTICS_SUPPORT
-define:ENABLE_CLOUD_LICENSE
-define:ENABLE_EDITOR_HUB_LICENSE
-define:ENABLE_WEBSOCKET_CLIENT
-define:ENABLE_GENERATE_NATIVE_PLUGINS_FOR_ASSEMBLIES_API
-define:ENABLE_DIRECTOR_AUDIO
-define:ENABLE_DIRECTOR_TEXTURE
-define:ENABLE_MANAGED_JOBS
-define:ENABLE_MANAGED_TRANSFORM_JOBS
-define:ENABLE_MANAGED_ANIMATION_JOBS
-define:ENABLE_MANAGED_AUDIO_JOBS
-define:ENABLE_MANAGED_UNITYTLS
-define:INCLUDE_DYNAMIC_GI
-define:ENABLE_SCRIPTING_GC_WBARRIERS
-define:PLATFORM_SUPPORTS_MONO
-define:RENDER_SOFTWARE_CURSOR
-define:ENABLE_VIDEO
-define:ENABLE_ACCELERATOR_CLIENT_DEBUGGING
-define:ENABLE_NAVIGATION_PACKAGE_DEBUG_VISUALIZATION
-define:ENABLE_NAVIGATION_HEIGHTMESH_RUNTIME_SUPPORT
-define:ENABLE_NAVIGATION_UI_REQUIRES_PACKAGE
-define:PLATFORM_STANDALONE
-define:TEXTCORE_1_0_OR_NEWER
-define:PLATFORM_STANDALONE_WIN
-define:UNITY_STANDALONE_WIN
-define:UNITY_STANDALONE
-define:UNITY_UGP_API
-define:ENABLE_RUNTIME_GI
-define:ENABLE_MOVIES
-define:ENABLE_NETWORK
-define:ENABLE_NVIDIA
-define:ENABLE_CRUNCH_TEXTURE_COMPRESSION
-define:ENABLE_OUT_OF_PROCESS_CRASH_HANDLER
-define:ENABLE_CLUSTER_SYNC
-define:ENABLE_CLUSTERINPUT
-define:PLATFORM_UPDATES_TIME_OUTSIDE_OF_PLAYER_LOOP
-define:GFXDEVICE_WAITFOREVENT_MESSAGEPUMP
-define:PLATFORM_INITIALIZES_MEMORY_MANAGER_EXPLICITLY
-define:ENABLE_MONO
-define:NET_4_6
-define:NET_UNITY_4_8
-define:ENABLE_PROFILER
-define:DEBUG
-define:TRACE
-define:UNITY_ASSERTIONS
-define:UNITY_EDITOR
-define:UNITY_EDITOR_64
-define:UNITY_EDITOR_WIN
-define:ENABLE_UNITY_COLLECTIONS_CHECKS
-define:ENABLE_BURST_AOT
-define:UNITY_TEAM_LICENSE
-define:ENABLE_CUSTOM_RENDER_TEXTURE
-define:ENABLE_DIRECTOR
-define:ENABLE_LOCALIZATION
-define:ENABLE_SPRITES
-define:ENABLE_TERRAIN
-define:ENABLE_TILEMAP
-define:ENABLE_TIMELINE
-define:ENABLE_INPUT_SYSTEM
-define:TEXTCORE_FONT_ENGINE_1_5_OR_NEWER
-define:UNITY_POST_PROCESSING_STACK_V2
-define:ODIN_VALIDATOR
-define:ODIN_VALIDATOR_3_1
-define:ODIN_INSPECTOR
-define:ODIN_INSPECTOR_3
-define:ODIN_INSPECTOR_3_1
-define:MAGICACLOTH2
-define:TextMeshPro
-define:DOTWEEN
-define:UNITY_PIPELINE_URP
-define:LiveScriptReload_Enabled
-define:LiveScriptReload_IncludeInBuild_Enabled
-define:CSHARP_7_OR_LATER
-define:CSHARP_7_3_OR_NEWER
-r:"Assets/LiveScriptReload/Plugins/FastScriptReload/Plugins/Roslyn/2021+/System.Runtime.CompilerServices.Unsafe.dll"
-r:"Assets/NuGet/Editor/NugetForUnity.dll"
-r:"Assets/NuGet/Editor/NuGetForUnity.PluginAPI.dll"
-r:"Assets/Packages/MessagePack.3.1.3/lib/netstandard2.1/MessagePack.dll"
-r:"Assets/Packages/MessagePack.Annotations.3.1.3/lib/netstandard2.0/MessagePack.Annotations.dll"
-r:"Assets/Packages/Microsoft.NET.StringTools.17.11.4/lib/netstandard2.0/Microsoft.NET.StringTools.dll"
-r:"Assets/Packages/System.Collections.Immutable.8.0.0/lib/netstandard2.0/System.Collections.Immutable.dll"
-r:"Assets/Plugins/Demigiant/DemiLib/Core/DemiLib.dll"
-r:"Assets/Plugins/Demigiant/DemiLib/Core/Editor/DemiEditor.dll"
-r:"Assets/Plugins/Demigiant/DOTween/DOTween.dll"
-r:"Assets/Plugins/Demigiant/DOTween/Editor/DOTweenEditor.dll"
-r:"Assets/Plugins/Demigiant/DOTweenPro/DOTweenPro.dll"
-r:"Assets/Plugins/Demigiant/DOTweenPro/Editor/DOTweenProEditor.dll"
-r:"Assets/Plugins/Protobuf/Google.Protobuf.dll"
-r:"Assets/Plugins/Protobuf/NuGet.Frameworks.dll"
-r:"Assets/Plugins/Sirenix/Assemblies/Sirenix.OdinInspector.Attributes.dll"
-r:"Assets/Plugins/Sirenix/Assemblies/Sirenix.OdinInspector.Editor.dll"
-r:"Assets/Plugins/Sirenix/Assemblies/Sirenix.OdinValidator.Editor.dll"
-r:"Assets/Plugins/Sirenix/Assemblies/Sirenix.Serialization.Config.dll"
-r:"Assets/Plugins/Sirenix/Assemblies/Sirenix.Serialization.dll"
-r:"Assets/Plugins/Sirenix/Assemblies/Sirenix.Utilities.dll"
-r:"Assets/Plugins/Sirenix/Assemblies/Sirenix.Utilities.Editor.dll"
-r:"Assets/TEngine/Editor/Localization/Unity XCode/UnityEditor.iOS_I2Loc.Xcode.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEditor.Graphs.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.CoreModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.DeviceSimulatorModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.DiagnosticsModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.EditorToolbarModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.GraphViewModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.PresetsUIModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.QuickSearchModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.SceneTemplateModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.SceneViewModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.TextCoreFontEngineModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.TextCoreTextEngineModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.UIBuilderModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.UIElementsModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.UIElementsSamplesModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.UnityConnectModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.AccessibilityModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.AIModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.AndroidJNIModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.AnimationModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.ARModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.AssetBundleModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.AudioModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.ClothModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.ClusterInputModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.ClusterRendererModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.ContentLoadModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.CoreModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.CrashReportingModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.DirectorModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.DSPGraphModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.GameCenterModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.GIModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.GridModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.HotReloadModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.ImageConversionModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.IMGUIModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.InputLegacyModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.InputModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.JSONSerializeModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.LocalizationModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.ParticleSystemModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.PerformanceReportingModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.Physics2DModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.PhysicsModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.ProfilerModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.PropertiesModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.ScreenCaptureModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.SharedInternalsModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.SpriteMaskModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.SpriteShapeModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.StreamingModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.SubstanceModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.SubsystemsModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.TerrainModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.TerrainPhysicsModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.TextCoreFontEngineModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.TextCoreTextEngineModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.TextRenderingModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.TilemapModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.TLSModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UIElementsModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UIModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UmbraModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityAnalyticsCommonModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityAnalyticsModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityConnectModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityCurlModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityTestProtocolModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestAssetBundleModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestAudioModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestTextureModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestWWWModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.VehiclesModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.VFXModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.VideoModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.VirtualTexturingModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.VRModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.WindModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.XRModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/PlaybackEngines/AndroidPlayer/Unity.Android.Gradle.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/PlaybackEngines/AndroidPlayer/Unity.Android.GradleProject.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/PlaybackEngines/AndroidPlayer/Unity.Android.Types.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/Microsoft.Win32.Primitives.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/netstandard.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.AppContext.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Buffers.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Collections.Concurrent.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Collections.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Collections.NonGeneric.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Collections.Specialized.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ComponentModel.Annotations.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ComponentModel.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ComponentModel.EventBasedAsync.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ComponentModel.Primitives.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ComponentModel.TypeConverter.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Console.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Data.Common.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.Contracts.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.Debug.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.FileVersionInfo.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.Process.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.StackTrace.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.TextWriterTraceListener.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.Tools.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.TraceSource.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Drawing.Primitives.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Dynamic.Runtime.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Globalization.Calendars.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Globalization.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Globalization.Extensions.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.Compression.ZipFile.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.FileSystem.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.FileSystem.DriveInfo.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.FileSystem.Primitives.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.FileSystem.Watcher.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.IsolatedStorage.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.MemoryMappedFiles.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.Pipes.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.UnmanagedMemoryStream.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Linq.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Linq.Expressions.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Linq.Parallel.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Linq.Queryable.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Memory.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Http.Rtc.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.NameResolution.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.NetworkInformation.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Ping.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Primitives.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Requests.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Security.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Sockets.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.WebHeaderCollection.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.WebSockets.Client.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.WebSockets.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ObjectModel.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.DispatchProxy.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.Emit.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.Emit.ILGeneration.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.Emit.Lightweight.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.Extensions.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.Primitives.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Resources.Reader.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Resources.ResourceManager.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Resources.Writer.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.CompilerServices.VisualC.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Extensions.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Handles.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.InteropServices.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.InteropServices.RuntimeInformation.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.InteropServices.WindowsRuntime.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Numerics.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Serialization.Formatters.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Serialization.Json.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Serialization.Primitives.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Serialization.Xml.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Claims.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Cryptography.Algorithms.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Cryptography.Csp.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Cryptography.Encoding.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Cryptography.Primitives.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Cryptography.X509Certificates.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Principal.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.SecureString.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ServiceModel.Duplex.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ServiceModel.Http.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ServiceModel.NetTcp.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ServiceModel.Primitives.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ServiceModel.Security.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Text.Encoding.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Text.Encoding.Extensions.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Text.RegularExpressions.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Overlapped.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Tasks.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Tasks.Extensions.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Tasks.Parallel.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Thread.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.ThreadPool.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Timer.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ValueTuple.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.ReaderWriter.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.XDocument.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.XmlDocument.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.XmlSerializer.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.XPath.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.XPath.XDocument.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Microsoft.CSharp.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/mscorlib.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.ComponentModel.Composition.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Core.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Data.DataSetExtensions.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Data.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Drawing.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.IO.Compression.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.IO.Compression.FileSystem.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Net.Http.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Numerics.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Numerics.Vectors.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Runtime.Serialization.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Transactions.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Xml.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Xml.Linq.dll"
-r:"Library/PackageCache/com.code-philosophy.hybridclr@59a3c3974a/Plugins/dnlib.dll"
-r:"Library/PackageCache/com.code-philosophy.hybridclr@59a3c3974a/Plugins/LZ4.dll"
-r:"Library/PackageCache/com.unity.collections@2.5.1/Unity.Collections.LowLevel.ILSupport/Unity.Collections.LowLevel.ILSupport.dll"
-r:"Library/PackageCache/com.unity.ext.nunit@2.0.3/net40/unity-custom/nunit.framework.dll"
-r:"Library/PackageCache/com.unity.nuget.mono-cecil@1.11.4/Mono.Cecil.dll"
-r:"Library/PackageCache/com.unity.nuget.newtonsoft-json@3.2.1/Runtime/Newtonsoft.Json.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/GameProto.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/TEngine.Runtime.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/UniTask.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/UnityEditor.UI.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/UnityEngine.UI.ref.dll"
-analyzer:"Assets/Packages/MessagePackAnalyzer.3.1.3/analyzers/roslyn4.3/cs/MessagePack.Analyzers.CodeFixes.dll"
-analyzer:"Assets/Packages/MessagePackAnalyzer.3.1.3/analyzers/roslyn4.3/cs/MessagePack.SourceGenerator.dll"
-analyzer:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Tools/Unity.SourceGenerators/Unity.Properties.SourceGenerator.dll"
-analyzer:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Tools/Unity.SourceGenerators/Unity.SourceGenerators.dll"
"Assets/GameScripts/HotFix/GameBase/BaseLogicSys.cs"
"Assets/GameScripts/HotFix/GameBase/BehaviourSingleton.cs"
"Assets/GameScripts/HotFix/GameBase/CRC32/CRC32.cs"
"Assets/GameScripts/HotFix/GameBase/CRC32/SafeProxy.cs"
"Assets/GameScripts/HotFix/GameBase/Data/DataManger.cs"
"Assets/GameScripts/HotFix/GameBase/Data/Model_Prop.cs"
"Assets/GameScripts/HotFix/GameBase/Data/ObservableDictionary.cs"
"Assets/GameScripts/HotFix/GameBase/Data/TableDescribe.cs"
"Assets/GameScripts/HotFix/GameBase/Data/TableMgr.cs"
"Assets/GameScripts/HotFix/GameBase/Data/UserDataModule.cs"
"Assets/GameScripts/HotFix/GameBase/DataModule/BaseTable/Achievement.cs"
"Assets/GameScripts/HotFix/GameBase/DataModule/BaseTable/Attribute.cs"
"Assets/GameScripts/HotFix/GameBase/DataModule/BaseTable/BuildTask.cs"
"Assets/GameScripts/HotFix/GameBase/DataModule/BaseTable/Campaign.cs"
"Assets/GameScripts/HotFix/GameBase/DataModule/BaseTable/ChatModule.cs"
"Assets/GameScripts/HotFix/GameBase/DataModule/BaseTable/ChatMoment.cs"
"Assets/GameScripts/HotFix/GameBase/DataModule/BaseTable/ChatObject.cs"
"Assets/GameScripts/HotFix/GameBase/DataModule/BaseTable/CommanderGrowth.cs"
"Assets/GameScripts/HotFix/GameBase/DataModule/BaseTable/Equipment.cs"
"Assets/GameScripts/HotFix/GameBase/DataModule/BaseTable/GameGuide.cs"
"Assets/GameScripts/HotFix/GameBase/DataModule/BaseTable/Laboratory.cs"
"Assets/GameScripts/HotFix/GameBase/DataModule/BaseTable/Levels.cs"
"Assets/GameScripts/HotFix/GameBase/DataModule/BaseTable/LevelsDeployment.cs"
"Assets/GameScripts/HotFix/GameBase/DataModule/BaseTable/Mecha.cs"
"Assets/GameScripts/HotFix/GameBase/DataModule/BaseTable/Notebook.cs"
"Assets/GameScripts/HotFix/GameBase/DataModule/BaseTable/PartArm.cs"
"Assets/GameScripts/HotFix/GameBase/DataModule/BaseTable/PartBody.cs"
"Assets/GameScripts/HotFix/GameBase/DataModule/BaseTable/PartEngine.cs"
"Assets/GameScripts/HotFix/GameBase/DataModule/BaseTable/PartHead.cs"
"Assets/GameScripts/HotFix/GameBase/DataModule/BaseTable/PartLeg.cs"
"Assets/GameScripts/HotFix/GameBase/DataModule/BaseTable/Passport.cs"
"Assets/GameScripts/HotFix/GameBase/DataModule/BaseTable/Props.cs"
"Assets/GameScripts/HotFix/GameBase/DataModule/BaseTable/RoleGrowth.cs"
"Assets/GameScripts/HotFix/GameBase/DataModule/BaseTable/RoleInfo.cs"
"Assets/GameScripts/HotFix/GameBase/DataModule/BaseTable/ShellEffect.cs"
"Assets/GameScripts/HotFix/GameBase/DataModule/BaseTable/Shells.cs"
"Assets/GameScripts/HotFix/GameBase/DataModule/BaseTable/ShoppingTrade.cs"
"Assets/GameScripts/HotFix/GameBase/DataModule/BaseTable/Tasks.cs"
"Assets/GameScripts/HotFix/GameBase/DataModule/BaseTable/Upgrade.cs"
"Assets/GameScripts/HotFix/GameBase/DataModule/BaseTable/Weapon.cs"
"Assets/GameScripts/HotFix/GameBase/DataModule/Table/Achievement.cs"
"Assets/GameScripts/HotFix/GameBase/DataModule/Table/Attribute.cs"
"Assets/GameScripts/HotFix/GameBase/DataModule/Table/BuildTask.cs"
"Assets/GameScripts/HotFix/GameBase/DataModule/Table/Campaign.cs"
"Assets/GameScripts/HotFix/GameBase/DataModule/Table/ChatModule.cs"
"Assets/GameScripts/HotFix/GameBase/DataModule/Table/ChatMoment.cs"
"Assets/GameScripts/HotFix/GameBase/DataModule/Table/ChatObject.cs"
"Assets/GameScripts/HotFix/GameBase/DataModule/Table/CommanderGrowth.cs"
"Assets/GameScripts/HotFix/GameBase/DataModule/Table/Equipment.cs"
"Assets/GameScripts/HotFix/GameBase/DataModule/Table/GameGuide.cs"
"Assets/GameScripts/HotFix/GameBase/DataModule/Table/Laboratory.cs"
"Assets/GameScripts/HotFix/GameBase/DataModule/Table/Levels.cs"
"Assets/GameScripts/HotFix/GameBase/DataModule/Table/LevelsDeployment.cs"
"Assets/GameScripts/HotFix/GameBase/DataModule/Table/Mecha.cs"
"Assets/GameScripts/HotFix/GameBase/DataModule/Table/Notebook.cs"
"Assets/GameScripts/HotFix/GameBase/DataModule/Table/PartArm.cs"
"Assets/GameScripts/HotFix/GameBase/DataModule/Table/PartBody.cs"
"Assets/GameScripts/HotFix/GameBase/DataModule/Table/PartEngine.cs"
"Assets/GameScripts/HotFix/GameBase/DataModule/Table/PartHead.cs"
"Assets/GameScripts/HotFix/GameBase/DataModule/Table/PartLeg.cs"
"Assets/GameScripts/HotFix/GameBase/DataModule/Table/Passport.cs"
"Assets/GameScripts/HotFix/GameBase/DataModule/Table/Props.cs"
"Assets/GameScripts/HotFix/GameBase/DataModule/Table/RoleGrowth.cs"
"Assets/GameScripts/HotFix/GameBase/DataModule/Table/RoleInfo.cs"
"Assets/GameScripts/HotFix/GameBase/DataModule/Table/ShellEffect.cs"
"Assets/GameScripts/HotFix/GameBase/DataModule/Table/Shells.cs"
"Assets/GameScripts/HotFix/GameBase/DataModule/Table/ShoppingTrade.cs"
"Assets/GameScripts/HotFix/GameBase/DataModule/Table/Tasks.cs"
"Assets/GameScripts/HotFix/GameBase/DataModule/Table/Upgrade.cs"
"Assets/GameScripts/HotFix/GameBase/DataModule/Table/Weapon.cs"
"Assets/GameScripts/HotFix/GameBase/DataModule/Tables.cs"
"Assets/GameScripts/HotFix/GameBase/DataModule/Tb/Achievement.cs"
"Assets/GameScripts/HotFix/GameBase/DataModule/Tb/ArmorPart.cs"
"Assets/GameScripts/HotFix/GameBase/DataModule/Tb/Attribute.cs"
"Assets/GameScripts/HotFix/GameBase/DataModule/Tb/Bone.cs"
"Assets/GameScripts/HotFix/GameBase/DataModule/Tb/BuildingDeployment.cs"
"Assets/GameScripts/HotFix/GameBase/DataModule/Tb/BuildTask.cs"
"Assets/GameScripts/HotFix/GameBase/DataModule/Tb/Campaign.cs"
"Assets/GameScripts/HotFix/GameBase/DataModule/Tb/ChatChoices.cs"
"Assets/GameScripts/HotFix/GameBase/DataModule/Tb/ChatComment.cs"
"Assets/GameScripts/HotFix/GameBase/DataModule/Tb/ChatCommentOption.cs"
"Assets/GameScripts/HotFix/GameBase/DataModule/Tb/ChatMessage.cs"
"Assets/GameScripts/HotFix/GameBase/DataModule/Tb/ChatModule.cs"
"Assets/GameScripts/HotFix/GameBase/DataModule/Tb/ChatMoment.cs"
"Assets/GameScripts/HotFix/GameBase/DataModule/Tb/ChatObject.cs"
"Assets/GameScripts/HotFix/GameBase/DataModule/Tb/CommanderGrowth.cs"
"Assets/GameScripts/HotFix/GameBase/DataModule/Tb/Engine.cs"
"Assets/GameScripts/HotFix/GameBase/DataModule/Tb/Equipment.cs"
"Assets/GameScripts/HotFix/GameBase/DataModule/Tb/GameGuide.cs"
"Assets/GameScripts/HotFix/GameBase/DataModule/Tb/InternalWeight.cs"
"Assets/GameScripts/HotFix/GameBase/DataModule/Tb/Laboratory.cs"
"Assets/GameScripts/HotFix/GameBase/DataModule/Tb/Levels.cs"
"Assets/GameScripts/HotFix/GameBase/DataModule/Tb/LevelsDeployment.cs"
"Assets/GameScripts/HotFix/GameBase/DataModule/Tb/Likability.cs"
"Assets/GameScripts/HotFix/GameBase/DataModule/Tb/Mecha.cs"
"Assets/GameScripts/HotFix/GameBase/DataModule/Tb/NoteBook.cs"
"Assets/GameScripts/HotFix/GameBase/DataModule/Tb/NoteBookTask.cs"
"Assets/GameScripts/HotFix/GameBase/DataModule/Tb/Passport.cs"
"Assets/GameScripts/HotFix/GameBase/DataModule/Tb/Props.cs"
"Assets/GameScripts/HotFix/GameBase/DataModule/Tb/RoleGrowth.cs"
"Assets/GameScripts/HotFix/GameBase/DataModule/Tb/RoleInfo.cs"
"Assets/GameScripts/HotFix/GameBase/DataModule/Tb/ShellEffect.cs"
"Assets/GameScripts/HotFix/GameBase/DataModule/Tb/Shells.cs"
"Assets/GameScripts/HotFix/GameBase/DataModule/Tb/ShoppingTrade.cs"
"Assets/GameScripts/HotFix/GameBase/DataModule/Tb/Tasks.cs"
"Assets/GameScripts/HotFix/GameBase/DataModule/Tb/Triggers.cs"
"Assets/GameScripts/HotFix/GameBase/DataModule/Tb/UnitDeployment.cs"
"Assets/GameScripts/HotFix/GameBase/DataModule/Tb/Upgrade.cs"
"Assets/GameScripts/HotFix/GameBase/DataModule/Tb/Weapon.cs"
"Assets/GameScripts/HotFix/GameBase/DataModule/vector2.cs"
"Assets/GameScripts/HotFix/GameBase/DataModule/vector3.cs"
"Assets/GameScripts/HotFix/GameBase/DataModule/vector4.cs"
"Assets/GameScripts/HotFix/GameBase/Encrypt/MD5.cs"
"Assets/GameScripts/HotFix/GameBase/GameNet.cs"
"Assets/GameScripts/HotFix/GameBase/GlobalDefined.cs"
"Assets/GameScripts/HotFix/GameBase/ILogicSys.cs"
"Assets/GameScripts/HotFix/GameBase/NetModule/HttpModule.cs"
"Assets/GameScripts/HotFix/GameBase/NetModule/NetDh.cs"
"Assets/GameScripts/HotFix/GameBase/NetModule/NetModule.cs"
"Assets/GameScripts/HotFix/GameBase/NetModule/NetPacket.cs"
"Assets/GameScripts/HotFix/GameBase/NetModule/NetPacketParser.cs"
"Assets/GameScripts/HotFix/GameBase/Singleton.cs"
"Assets/GameScripts/HotFix/GameBase/SingletonBehaviour.cs"
"Assets/GameScripts/HotFix/GameBase/SingletonSystem.cs"
"Assets/GameScripts/HotFix/GameBase/UtilsAnalysis.cs"
"Assets/GameScripts/HotFix/GameBase/UtilsView.cs"
"Assets/GameScripts/HotFix/GameBase/VertexEffects/BoxOutline.cs"
"Assets/GameScripts/HotFix/GameBase/VertexEffects/CircleOutline.cs"
"Assets/GameScripts/HotFix/GameBase/VertexEffects/ListPool.cs"
"Assets/GameScripts/HotFix/GameBase/VertexEffects/ModifiedShadow.cs"
"Assets/GameScripts/HotFix/GameBase/VertexEffects/ObjectPool1.cs"
"Assets/GameScripts/HotFix/GameBase/VertexEffects/Outline8.cs"
-langversion:9.0
/deterministic
/optimize-
/debug:portable
/nologo
/RuntimeMetadataVersion:v4.0.30319
/nowarn:0169
/nowarn:0649
/nowarn:0282
/nowarn:1701
/nowarn:1702
/utf8output
/preferreduilang:en-US
/additionalfile:"Library/Bee/artifacts/1900b0aEDbg.dag/GameBase.UnityAdditionalFile.txt"