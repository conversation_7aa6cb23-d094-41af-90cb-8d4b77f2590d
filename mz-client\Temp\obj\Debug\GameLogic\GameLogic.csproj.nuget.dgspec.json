{"format": 1, "restore": {"D:\\Work\\military-zone-client\\mz-client\\GameLogic.csproj": {}}, "projects": {"D:\\Work\\military-zone-client\\mz-client\\DG.Tweening.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Work\\military-zone-client\\mz-client\\DG.Tweening.csproj", "projectName": "<PERSON><PERSON><PERSON>T<PERSON>", "projectPath": "D:\\Work\\military-zone-client\\mz-client\\DG.Tweening.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Work\\military-zone-client\\mz-client\\Temp\\obj\\Debug\\DG.Tweening\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.104\\RuntimeIdentifierGraph.json"}}}, "D:\\Work\\military-zone-client\\mz-client\\GameBase.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Work\\military-zone-client\\mz-client\\GameBase.csproj", "projectName": "GameBase", "projectPath": "D:\\Work\\military-zone-client\\mz-client\\GameBase.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Work\\military-zone-client\\mz-client\\Temp\\obj\\Debug\\GameBase\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Work\\military-zone-client\\mz-client\\GameProto.csproj": {"projectPath": "D:\\Work\\military-zone-client\\mz-client\\GameProto.csproj"}, "D:\\Work\\military-zone-client\\mz-client\\TEngine.Runtime.csproj": {"projectPath": "D:\\Work\\military-zone-client\\mz-client\\TEngine.Runtime.csproj"}, "D:\\Work\\military-zone-client\\mz-client\\UniTask.csproj": {"projectPath": "D:\\Work\\military-zone-client\\mz-client\\UniTask.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.104\\RuntimeIdentifierGraph.json"}}}, "D:\\Work\\military-zone-client\\mz-client\\GameCommon.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Work\\military-zone-client\\mz-client\\GameCommon.csproj", "projectName": "GameCommon", "projectPath": "D:\\Work\\military-zone-client\\mz-client\\GameCommon.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Work\\military-zone-client\\mz-client\\Temp\\obj\\Debug\\GameCommon\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Work\\military-zone-client\\mz-client\\GameBase.csproj": {"projectPath": "D:\\Work\\military-zone-client\\mz-client\\GameBase.csproj"}, "D:\\Work\\military-zone-client\\mz-client\\GameProto.csproj": {"projectPath": "D:\\Work\\military-zone-client\\mz-client\\GameProto.csproj"}, "D:\\Work\\military-zone-client\\mz-client\\TEngine.Runtime.csproj": {"projectPath": "D:\\Work\\military-zone-client\\mz-client\\TEngine.Runtime.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.104\\RuntimeIdentifierGraph.json"}}}, "D:\\Work\\military-zone-client\\mz-client\\GameLogic.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Work\\military-zone-client\\mz-client\\GameLogic.csproj", "projectName": "GameLogic", "projectPath": "D:\\Work\\military-zone-client\\mz-client\\GameLogic.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Work\\military-zone-client\\mz-client\\Temp\\obj\\Debug\\GameLogic\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Work\\military-zone-client\\mz-client\\DG.Tweening.csproj": {"projectPath": "D:\\Work\\military-zone-client\\mz-client\\DG.Tweening.csproj"}, "D:\\Work\\military-zone-client\\mz-client\\GameBase.csproj": {"projectPath": "D:\\Work\\military-zone-client\\mz-client\\GameBase.csproj"}, "D:\\Work\\military-zone-client\\mz-client\\GameCommon.csproj": {"projectPath": "D:\\Work\\military-zone-client\\mz-client\\GameCommon.csproj"}, "D:\\Work\\military-zone-client\\mz-client\\GameProto.csproj": {"projectPath": "D:\\Work\\military-zone-client\\mz-client\\GameProto.csproj"}, "D:\\Work\\military-zone-client\\mz-client\\JUTPS.csproj": {"projectPath": "D:\\Work\\military-zone-client\\mz-client\\JUTPS.csproj"}, "D:\\Work\\military-zone-client\\mz-client\\PhysicsTankMaker.csproj": {"projectPath": "D:\\Work\\military-zone-client\\mz-client\\PhysicsTankMaker.csproj"}, "D:\\Work\\military-zone-client\\mz-client\\TEngine.Runtime.csproj": {"projectPath": "D:\\Work\\military-zone-client\\mz-client\\TEngine.Runtime.csproj"}, "D:\\Work\\military-zone-client\\mz-client\\UniTask.csproj": {"projectPath": "D:\\Work\\military-zone-client\\mz-client\\UniTask.csproj"}, "D:\\Work\\military-zone-client\\mz-client\\YooAsset.csproj": {"projectPath": "D:\\Work\\military-zone-client\\mz-client\\YooAsset.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.104\\RuntimeIdentifierGraph.json"}}}, "D:\\Work\\military-zone-client\\mz-client\\GameProto.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Work\\military-zone-client\\mz-client\\GameProto.csproj", "projectName": "GameProto", "projectPath": "D:\\Work\\military-zone-client\\mz-client\\GameProto.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Work\\military-zone-client\\mz-client\\Temp\\obj\\Debug\\GameProto\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.104\\RuntimeIdentifierGraph.json"}}}, "D:\\Work\\military-zone-client\\mz-client\\JUTPS.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Work\\military-zone-client\\mz-client\\JUTPS.csproj", "projectName": "JUTPS", "projectPath": "D:\\Work\\military-zone-client\\mz-client\\JUTPS.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Work\\military-zone-client\\mz-client\\Temp\\obj\\Debug\\JUTPS\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Work\\military-zone-client\\mz-client\\GameCommon.csproj": {"projectPath": "D:\\Work\\military-zone-client\\mz-client\\GameCommon.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.104\\RuntimeIdentifierGraph.json"}}}, "D:\\Work\\military-zone-client\\mz-client\\PhysicsTankMaker.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Work\\military-zone-client\\mz-client\\PhysicsTankMaker.csproj", "projectName": "PhysicsTankMaker", "projectPath": "D:\\Work\\military-zone-client\\mz-client\\PhysicsTankMaker.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Work\\military-zone-client\\mz-client\\Temp\\obj\\Debug\\PhysicsTankMaker\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Work\\military-zone-client\\mz-client\\GameCommon.csproj": {"projectPath": "D:\\Work\\military-zone-client\\mz-client\\GameCommon.csproj"}, "D:\\Work\\military-zone-client\\mz-client\\TEngine.Runtime.csproj": {"projectPath": "D:\\Work\\military-zone-client\\mz-client\\TEngine.Runtime.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.104\\RuntimeIdentifierGraph.json"}}}, "D:\\Work\\military-zone-client\\mz-client\\TEngine.Runtime.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Work\\military-zone-client\\mz-client\\TEngine.Runtime.csproj", "projectName": "TEngine.Runtime", "projectPath": "D:\\Work\\military-zone-client\\mz-client\\TEngine.Runtime.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Work\\military-zone-client\\mz-client\\Temp\\obj\\Debug\\TEngine.Runtime\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Work\\military-zone-client\\mz-client\\UniTask.csproj": {"projectPath": "D:\\Work\\military-zone-client\\mz-client\\UniTask.csproj"}, "D:\\Work\\military-zone-client\\mz-client\\YooAsset.csproj": {"projectPath": "D:\\Work\\military-zone-client\\mz-client\\YooAsset.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.104\\RuntimeIdentifierGraph.json"}}}, "D:\\Work\\military-zone-client\\mz-client\\UniTask.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Work\\military-zone-client\\mz-client\\UniTask.csproj", "projectName": "UniTask", "projectPath": "D:\\Work\\military-zone-client\\mz-client\\UniTask.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Work\\military-zone-client\\mz-client\\Temp\\obj\\Debug\\UniTask\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Work\\military-zone-client\\mz-client\\YooAsset.csproj": {"projectPath": "D:\\Work\\military-zone-client\\mz-client\\YooAsset.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.104\\RuntimeIdentifierGraph.json"}}}, "D:\\Work\\military-zone-client\\mz-client\\YooAsset.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Work\\military-zone-client\\mz-client\\YooAsset.csproj", "projectName": "YooAsset", "projectPath": "D:\\Work\\military-zone-client\\mz-client\\YooAsset.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Work\\military-zone-client\\mz-client\\Temp\\obj\\Debug\\YooAsset\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.104\\RuntimeIdentifierGraph.json"}}}}}