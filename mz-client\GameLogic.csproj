﻿<Project ToolsVersion="Current">
  <!-- Generated file, do not modify, your changes will be overwritten (use AssetPostprocessor.OnGeneratedCSProject) -->
  <PropertyGroup>
    <BaseIntermediateOutputPath>Temp\obj\$(Configuration)\$(MSBuildProjectName)</BaseIntermediateOutputPath>
    <IntermediateOutputPath>$(BaseIntermediateOutputPath)</IntermediateOutputPath>
  </PropertyGroup>
  <Import Project="Sdk.props" Sdk="Microsoft.NET.Sdk" />
  <ItemGroup>
    <ProjectCapability Include="Unity" />
  </ItemGroup>
  <PropertyGroup>
    <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
    <EnableDefaultItems>false</EnableDefaultItems>
    <AppendTargetFrameworkToOutputPath>false</AppendTargetFrameworkToOutputPath>
    <LangVersion>9.0</LangVersion>
    <Configurations>Debug;Release</Configurations>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <RootNamespace>GameLogic</RootNamespace>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <AssemblyName>GameLogic</AssemblyName>
    <TargetFramework>netstandard2.1</TargetFramework>
    <BaseDirectory>.</BaseDirectory>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>Temp\bin\Debug\</OutputPath>
    <DefineConstants>UNITY_2022_3_51;UNITY_2022_3;UNITY_2022;UNITY_5_3_OR_NEWER;UNITY_5_4_OR_NEWER;UNITY_5_5_OR_NEWER;UNITY_5_6_OR_NEWER;UNITY_2017_1_OR_NEWER;UNITY_2017_2_OR_NEWER;UNITY_2017_3_OR_NEWER;UNITY_2017_4_OR_NEWER;UNITY_2018_1_OR_NEWER;UNITY_2018_2_OR_NEWER;UNITY_2018_3_OR_NEWER;UNITY_2018_4_OR_NEWER;UNITY_2019_1_OR_NEWER;UNITY_2019_2_OR_NEWER;UNITY_2019_3_OR_NEWER;UNITY_2019_4_OR_NEWER;UNITY_2020_1_OR_NEWER;UNITY_2020_2_OR_NEWER;UNITY_2020_3_OR_NEWER;UNITY_2021_1_OR_NEWER;UNITY_2021_2_OR_NEWER;UNITY_2021_3_OR_NEWER;UNITY_2022_1_OR_NEWER;UNITY_2022_2_OR_NEWER;UNITY_2022_3_OR_NEWER;PLATFORM_ARCH_64;UNITY_64;UNITY_INCLUDE_TESTS;ENABLE_AR;ENABLE_AUDIO;ENABLE_CACHING;ENABLE_CLOTH;ENABLE_EVENT_QUEUE;ENABLE_MICROPHONE;ENABLE_MULTIPLE_DISPLAYS;ENABLE_PHYSICS;ENABLE_TEXTURE_STREAMING;ENABLE_VIRTUALTEXTURING;ENABLE_LZMA;ENABLE_UNITYEVENTS;ENABLE_VR;ENABLE_WEBCAM;ENABLE_UNITYWEBREQUEST;ENABLE_WWW;ENABLE_CLOUD_SERVICES;ENABLE_CLOUD_SERVICES_ADS;ENABLE_CLOUD_SERVICES_USE_WEBREQUEST;ENABLE_CLOUD_SERVICES_CRASH_REPORTING;ENABLE_CLOUD_SERVICES_PURCHASING;ENABLE_CLOUD_SERVICES_ANALYTICS;ENABLE_CLOUD_SERVICES_BUILD;ENABLE_EDITOR_GAME_SERVICES;ENABLE_UNITY_GAME_SERVICES_ANALYTICS_SUPPORT;ENABLE_CLOUD_LICENSE;ENABLE_EDITOR_HUB_LICENSE;ENABLE_WEBSOCKET_CLIENT;ENABLE_GENERATE_NATIVE_PLUGINS_FOR_ASSEMBLIES_API;ENABLE_DIRECTOR_AUDIO;ENABLE_DIRECTOR_TEXTURE;ENABLE_MANAGED_JOBS;ENABLE_MANAGED_TRANSFORM_JOBS;ENABLE_MANAGED_ANIMATION_JOBS;ENABLE_MANAGED_AUDIO_JOBS;ENABLE_MANAGED_UNITYTLS;INCLUDE_DYNAMIC_GI;ENABLE_SCRIPTING_GC_WBARRIERS;PLATFORM_SUPPORTS_MONO;RENDER_SOFTWARE_CURSOR;ENABLE_VIDEO;ENABLE_ACCELERATOR_CLIENT_DEBUGGING;ENABLE_NAVIGATION_PACKAGE_DEBUG_VISUALIZATION;ENABLE_NAVIGATION_HEIGHTMESH_RUNTIME_SUPPORT;ENABLE_NAVIGATION_UI_REQUIRES_PACKAGE;PLATFORM_STANDALONE;TEXTCORE_1_0_OR_NEWER;PLATFORM_STANDALONE_WIN;UNITY_STANDALONE_WIN;UNITY_STANDALONE;UNITY_UGP_API;ENABLE_RUNTIME_GI;ENABLE_MOVIES;ENABLE_NETWORK;ENABLE_NVIDIA;ENABLE_CRUNCH_TEXTURE_COMPRESSION;ENABLE_OUT_OF_PROCESS_CRASH_HANDLER;ENABLE_CLUSTER_SYNC;ENABLE_CLUSTERINPUT;PLATFORM_UPDATES_TIME_OUTSIDE_OF_PLAYER_LOOP;GFXDEVICE_WAITFOREVENT_MESSAGEPUMP;PLATFORM_INITIALIZES_MEMORY_MANAGER_EXPLICITLY;ENABLE_MONO;NET_4_6;NET_UNITY_4_8;ENABLE_PROFILER;DEBUG;TRACE;UNITY_ASSERTIONS;UNITY_EDITOR;UNITY_EDITOR_64;UNITY_EDITOR_WIN;ENABLE_UNITY_COLLECTIONS_CHECKS;ENABLE_BURST_AOT;UNITY_TEAM_LICENSE;ENABLE_CUSTOM_RENDER_TEXTURE;ENABLE_DIRECTOR;ENABLE_LOCALIZATION;ENABLE_SPRITES;ENABLE_TERRAIN;ENABLE_TILEMAP;ENABLE_TIMELINE;ENABLE_INPUT_SYSTEM;TEXTCORE_FONT_ENGINE_1_5_OR_NEWER;UNITY_POST_PROCESSING_STACK_V2;ODIN_VALIDATOR;ODIN_VALIDATOR_3_1;ODIN_INSPECTOR;ODIN_INSPECTOR_3;ODIN_INSPECTOR_3_1;MAGICACLOTH2;TextMeshPro;DOTWEEN;UNITY_PIPELINE_URP;LiveScriptReload_Enabled;LiveScriptReload_IncludeInBuild_Enabled;ENABLE_URP;CSHARP_7_OR_LATER;CSHARP_7_3_OR_NEWER</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <NoWarn>0169;USG0001</NoWarn>
    <AllowUnsafeBlocks>False</AllowUnsafeBlocks>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>Temp\bin\Release\</OutputPath>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <NoWarn>0169;USG0001</NoWarn>
    <AllowUnsafeBlocks>False</AllowUnsafeBlocks>
  </PropertyGroup>
  <PropertyGroup>
    <NoStandardLibraries>true</NoStandardLibraries>
    <NoStdLib>true</NoStdLib>
    <NoConfig>true</NoConfig>
    <DisableImplicitFrameworkReferences>true</DisableImplicitFrameworkReferences>
    <MSBuildWarningsAsMessages>MSB3277</MSBuildWarningsAsMessages>
  </PropertyGroup>
  <PropertyGroup>
    <UnityProjectGenerator>Package</UnityProjectGenerator>
    <UnityProjectGeneratorVersion>2.0.22</UnityProjectGeneratorVersion>
    <UnityProjectGeneratorStyle>SDK</UnityProjectGeneratorStyle>
    <UnityProjectType>Game:1</UnityProjectType>
    <UnityBuildTarget>StandaloneWindows64:19</UnityBuildTarget>
    <UnityVersion>2022.3.51f1c1</UnityVersion>
  </PropertyGroup>
  <ItemGroup>
    <Analyzer Include="C:\Users\<USER>\.vscode\extensions\visualstudiotoolsforunity.vstuc-1.1.2\Analyzers\Microsoft.Unity.Analyzers.dll" />
    <Analyzer Include="D:\Work\military-zone-client\mz-client\Assets\Packages\MessagePackAnalyzer.3.1.3\analyzers\roslyn4.3\cs\MessagePack.Analyzers.CodeFixes.dll" />
    <Analyzer Include="D:\Work\military-zone-client\mz-client\Assets\Packages\MessagePackAnalyzer.3.1.3\analyzers\roslyn4.3\cs\MessagePack.SourceGenerator.dll" />
    <Analyzer Include="D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\Tools\Unity.SourceGenerators\Unity.SourceGenerators.dll" />
    <Analyzer Include="D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\Tools\Unity.SourceGenerators\Unity.Properties.SourceGenerator.dll" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\TpsTest\CharacterSwitchSystem\RoleCrouchTest.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\Battle\BattleCamera.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\UI\UIBattleTipDialog.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\Battle\AI\Node\AutoAttack\RepositioningNode.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\Battle\AI\Node\PlayerCommand\CmdForwardNode.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\UI\UIEmailItem.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\UI\UIBattleHeadItem.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\Battle\AI\Node\MoveToTargetNode.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\Battle\AI\CommandHandler\IPhaseCommandHandler.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\Battle\AI\CommandHandler\ShootTargetCommandHandler.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\UI\UILabSub1.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\UI\UIShopRechargeItem.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\UI\UILabMainPropItem.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\UI\UIBattleChoice.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\UI\UIActivityNotice.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\GameApp_RegisterSystem.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\TpsTest\CharacterSwitchSystem\MultiCharacterSceneSetup.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\UI\UICulEquipmentItem.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\UI\UIDispatchItemInfo.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\TpsTest\CharacterSwitchSystem\CharacterSwitchInput.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\Battle\AI\Node\PlayerCommand\CmdRetreatNode.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\Common\DataBind\DataBindingManager.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\UI\UIChatMoment.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\Battle\BattleTickManager.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\Battle\UIBattleUnitHealthBar.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\UI\UIVictory3.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\UI\UICulBulletAttrDialog.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\TpsTest\CameraDirectionAgentMover.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\Battle\Unit\UnitAI.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\TpsTest\CharacterSwitchSystem\CharacterManager.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\Battle\AI\Node\PlayerCommand\IInteractionStrategy.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\UI\UILabSecondPropItem.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\UI\DialogTipsText.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\Battle\Perception\UnitPerceptionSystem.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\Battle\AI\CommandHandler\IInstantCommandHandler.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\Battle\AI\Node\WaitUntilReachTargetNode.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\UI\UIBattleMainPage.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\UI\UIChapter.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\UI\UIChatList.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\Battle\AI\Command\Command.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\UI\UILogin.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\Common\CodeTypes\CodeTypes.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\Battle\AI\Behavior\RoleAttackBehavior.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\UI\UIPassportItem.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\Battle\Camera\CommanderCamera.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\UI\UIEmail.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\UI\UITaskDailyItem.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\UI\UICulGrowUp.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\UI\UICulSkillDialog.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\UI\UICulEquipmentDialog.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\TpsTest\CustomizeMove.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\Battle\AI\Command\CommandManager.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\UI\UIChatMomentImageItem.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\UI\UICulBoneInfoItem.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\UI\UIBarrackItem.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\Battle\AI\Node\PlayerCommand\CmdQuicklyDisperseNode.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\UI\UICulReformAttrItem.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\UI\UICulBondInfo.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\Battle\Context\TypedBlackboardBase.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\Battle\Unit\UniOperationBase.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\UI\UICulReformPItem.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\UI\DialogTipsCallback.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\TpsTest\TestMoveUI.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\Battle\AI\Command\CommandDispatcher.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\UI\UICulSkinItem.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\UI\UITaskSub2.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\UI\UIChatMsgSelfItem.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\UI\UIRoleSelect.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\UI\UIFormationAvaterItem.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\UI\UICulArmorInfo.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\UI\UIChapterLevelInfo.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\Battle\BattleUnitClick.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\Battle\Unit\RoleController.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\Common\BindPropCount.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\Battle\BattleDefined.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\Battle\AI\CommandHandler\EjectionCmdHandler.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\Battle\AI\Node\PostureChangeNode.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\UI\UIGameGuideItem.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\Battle\AI\CommandHandler\MoveToCmdHandler.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\Battle\AI\Node\AutoAttack\FindNewHateTargetNode.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\Battle\Context\TeamBlackboard.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\UI\DialogCurrencyTip.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\UI\UIRoleSelectItem.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\Battle\AI\Cover\CoverObject.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\UI\UIChatMomentInfo.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\UI\UICulMainWeaponDialog.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\TpsTest\ShootTest.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\UI\UIHallKbnItem.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\UI\UIChatContactItem.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\Battle\AI\CommandHandler\BumpCommandHandler.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\UI\UITaskSub1.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\Common\Common3DNodeView.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\TpsTest\JoyCoin\PreviewJoyController.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\TpsTest\JoyCoin\JoyMove.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\Battle\BattleCameraManager.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\Battle\AI\Node\PlayerCommand\CmdFollowMeNode.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\TpsTest\JoyCoin\JoystickController.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\Battle\Perception\TeamPerceptionSystem.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\Battle\AI\Node\PlayerCommand\MoveBackNode.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\Hall\HallSystem.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\UI\UILaboratory.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\UI\UIWareHouseInfo.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\Battle\AI\Node\AutoAttack\ChaseHateTargetNode.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\UI\UIBattleStateTPS.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\UI\UIPassport.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\UI\UICultivate.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\UI\UIAchievement.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\Battle\TargetSelection\Filters\AllyOnlySelection.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\Battle\AI\CommandHandler\StopMoveCommandHandler.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\TpsTest\PrefabShooter.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\Battle\AI\Command\PhaseCommandExecutor.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\Battle\Context\BattleContext.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\Battle\Context\BattleConstants.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\UI\UIViewMode.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\Battle\AI\TargetButtonHandler.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\Common\CodeTypes\Attribute\BaseAttribute.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\Battle\TargetSelection\Filters\EnemyOnlySelection.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\UI\UIShopSubMain.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\UI\UIBattleStatePropInfo.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\UI\UILoading.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\UI\UICulArmor.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\OnEnterGameAppProcedure.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\UI\UIActivityCenter.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\Battle\AI\Node\PlayerCommand\InteractiveCommandNode.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\UI\UIChatMomentLargeImgItem.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\UI\UICulReformMItem.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\UI\UIShopMInfoItem.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\TpsTest\JoyCoin\HoldToJoystickController.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\UI\UIBattlePrepare.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\Battle\TargetSelection\ITargetSelectionFilter.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\Battle\Unit\BaseController.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\Battle\AI\Node\CollisionNode.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\TpsTest\UI\UIManager.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\UI\UITaskMainItem.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\UI\UIShopSub3.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\UI\UIHomeLand.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\UI\UIShopSkinHeadItem.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\Battle\AI\Node\GearChangeNode.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\TpsTest\BehaviorTreeDebugger.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\Battle\PoolManager.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\UI\UICulReformItemInfo.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\UI\UIEnemyIndicator.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\UI\UIChatMsgRoleItem.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\UI\UIBattleStatePropItem.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\UI\UILabSub4.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\Common\TFTRadarChart.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\UI\UIActivityListItem.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\UI\UIGameSetting.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\Battle\AI\Node\AutoAttack\HandleVisionObstructionNode.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\UI\UIShopTradeMItem.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\Battle\AI\Node\BehaviorTreeNode.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\UI\UIShopSkinInfo.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\UI\UIWareHouse.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\TpsTest\AutoDestroyOnImpact.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\UI\UIPlotDialogue.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\Battle\AI\TargetingSystem.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\UI\UIShopMaterialItem.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\Battle\BattleMiniMapController.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\Battle\AI\Node\AutoAttack\ShootWithPhasesNode.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\Battle\AI\Node\PlayerCommand\CmdChargeNode.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\Battle\BattleLeaderMove.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\UI\UILabPItem.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\UI\UILabAvaterItem.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\Battle\Unit\UnitActor.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\UI\UITaskMainTaskInfo.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\Battle\AI\Node\SelectorNode.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\UI\UIShopSub4.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\UI\UIBattleLeaderItem.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\UI\UIDisAvaterItem.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\UI\UIShopSub2.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\UI\UICampaignItem.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\UI\UIVitcoryRewardItem.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\Battle\AI\CoroutineRunner.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\UI\UICulAvater.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\UI\UIPInfoSecretaryItem.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\Battle\AI\Cover\CoverPointManager.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\UI\UILabSub2.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\UI\UICulReForm.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\UI\UICulSkillItem.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\UI\UIVictory3StartItem.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\UI\UIVictory2.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\UI\UIDispatchItem.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\TpsTest\CharacterSwitchSystem\CharacterSwitchUI.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\Battle\AI\Node\AutoAttack\AutoAttackEnabledNode.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\UI\UIBattleStateRTS.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\Battle\BattleEventTrigger.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\Battle\BattleWeaponFly.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\Event\EventInterfaceImpAttribute.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\Battle\BattleWeaponBase.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\UI\UIVictory1.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\UI\UIWareHouseProp.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\UI\UIFormationQueueItem.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\UI\UIChatDialog.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\Battle\AI\Formation\BattleFormationController.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\Common\CodeTypes\UnOrderMultiMapSet.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\UI\UIDispatch.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\Battle\AI\Command\RushCommand.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\UI\DialogItemAwardProp.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\TpsTest\CharacterSwitchSystem\CharacterIndicator.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\Battle\AI\Node\PlayerCommand\CmdFindCoverNode.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\TpsTest\Aoe\AoeTargetingManager.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\UI\UIShopSkinSeriesItem.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\Battle\Context\CommandTypeComparer.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\Battle\TargetSelection\UI\TargetSelectionUI.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\Battle\Context\UnitBlackboard.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\UI\UIAlarm.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\Battle\BattleAirStrikeBomb.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\Battle\AI\Node\PlayerCommand\SelectHateTargetStrategy.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\Battle\AI\Node\AutoAttack\HandleCrowdingNode.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\Battle\TargetSelection\TargetSelectionContext.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\Battle\Unit\UniOperationAi.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\Battle\AI\Node\PlayerCommand\CmdStandingNode.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\UI\UIChatCommentItem.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\Battle\AI\CommandHandler\IReactiveCommandHandler.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\Battle\Unit\TankController.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\UI\UIBattleTargetSelect.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\Battle\AI\Command\ReactiveCommandExecutor.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\UI\UIBattleCmdItem.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\UI\UIBatPrepItem.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\Battle\Context\BlackboardKeys.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\UI\UIChapterLevelItem.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\Event\EventInterfaceHelper.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\Common\ZoomAndDragImage.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\GameApp.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\Battle\AI\Behavior\TankAttackBehavior.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\UI\UIPlayerInfo.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\TpsTest\TankAttackTest.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\TpsTest\SupportPreviewMover.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\UI\UIAchiItem.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\Common\Extend\CustomToggle.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\UI\UIGameGuide.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\UI\UIFormation.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\UI\UIGameGuideInfoItem.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\UI\UIBarrack.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\Battle\AI\Node\CommandDispatchNode.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\UI\UIWaiting.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\Event\RegisterEventInterface_Logic.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\Battle\Perception\PerceptionManager.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\UI\UICulSkinInfo.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\Battle\AI\Node\SequenceNode.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\Common\ViewUtils.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\UI\UILabSub3.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\Battle\Unit\RoleMoveComponent.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\TpsTest\CharacterSwitchSystem\SetupCustomInputs.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\UI\UIShopSub1.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\UI\DialogTipsAward1.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\UI\UIAchiRewardItem.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\TpsTest\JoyCoin\TouchDragHandler.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\Event\RegisterEventInterface_UI.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\UI\UIDisRewardItem.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\UI\UIBattleStarInfoDialog.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\UI\UINoteBook.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\Battle\TeamCommandExecutor.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\UI\UIHall.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\UI\UIChatMessageInfo.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\UI\UIDispatch2.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\Battle\AI\Node\JoystickMoveNode.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\Common\ScrollPageController.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\Common\WarpperObject\SRoleWrapper.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\Battle\TargetSelection\Highlight\Highlightable.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\Battle\TargetSelection\TargetSelectionSystem.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\UI\UICampaign.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\UI\UIPInfoHallImgItem.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\UI\UICulAttrInfo.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\UI\UIBattleStatePropDialog.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\UI\UICulBulletItem.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\UI\UIShop.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\UI\UITask.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\Battle\BattleSystem.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\Battle\BattleAirStrikePlane.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\UI\UIChatMomentItem.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\UI\UIBattleRoleInfoDialog.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\UI\UIShopTradeMInfoItem.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\Battle\AI\Command\InstantCommandExecutor.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\Battle\AI\TargetProjector.cs" />
    <Compile Include="Assets\GameScripts\HotFix\GameLogic\UI\UIPInfoSkinItem.cs" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Assets\GameScripts\HotFix\GameLogic\GameLogic.asmdef" />
  </ItemGroup>
  <ItemGroup>
    <Reference Include="UnityEngine">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AIModule">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.AIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ARModule">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.ARModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AccessibilityModule">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.AccessibilityModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AndroidJNIModule">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.AndroidJNIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AnimationModule">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.AnimationModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AssetBundleModule">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.AssetBundleModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.AudioModule">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.AudioModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ClothModule">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.ClothModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ClusterInputModule">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.ClusterInputModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ClusterRendererModule">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.ClusterRendererModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ContentLoadModule">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.ContentLoadModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.CoreModule">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.CoreModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.CrashReportingModule">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.CrashReportingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.DSPGraphModule">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.DSPGraphModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.DirectorModule">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.DirectorModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.GIModule">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.GIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.GameCenterModule">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.GameCenterModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.GridModule">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.GridModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.HotReloadModule">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.HotReloadModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.IMGUIModule">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.IMGUIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ImageConversionModule">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.ImageConversionModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.InputModule">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.InputModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.InputLegacyModule">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.InputLegacyModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.JSONSerializeModule">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.JSONSerializeModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.LocalizationModule">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.LocalizationModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ParticleSystemModule">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.ParticleSystemModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.PerformanceReportingModule">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.PerformanceReportingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.PhysicsModule">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.PhysicsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.Physics2DModule">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.Physics2DModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ProfilerModule">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.ProfilerModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.PropertiesModule">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.PropertiesModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.ScreenCaptureModule">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.ScreenCaptureModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.SharedInternalsModule">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.SharedInternalsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.SpriteMaskModule">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.SpriteMaskModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.SpriteShapeModule">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.SpriteShapeModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.StreamingModule">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.StreamingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.SubstanceModule">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.SubstanceModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.SubsystemsModule">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.SubsystemsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TLSModule">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.TLSModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TerrainModule">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.TerrainModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TerrainPhysicsModule">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.TerrainPhysicsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TextCoreFontEngineModule">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.TextCoreFontEngineModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TextCoreTextEngineModule">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.TextCoreTextEngineModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TextRenderingModule">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.TextRenderingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.TilemapModule">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.TilemapModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UIModule">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.UIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UIElementsModule">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.UIElementsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UmbraModule">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.UmbraModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityAnalyticsModule">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityAnalyticsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityAnalyticsCommonModule">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityAnalyticsCommonModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityConnectModule">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityConnectModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityCurlModule">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityCurlModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityTestProtocolModule">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityTestProtocolModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestModule">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestAssetBundleModule">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestAssetBundleModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestAudioModule">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestAudioModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestTextureModule">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestTextureModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UnityWebRequestWWWModule">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.UnityWebRequestWWWModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.VFXModule">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.VFXModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.VRModule">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.VRModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.VehiclesModule">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.VehiclesModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.VideoModule">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.VideoModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.VirtualTexturingModule">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.VirtualTexturingModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.WindModule">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.WindModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.XRModule">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\Managed\UnityEngine\UnityEngine.XRModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\Managed\UnityEngine\UnityEditor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.CoreModule">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\Managed\UnityEngine\UnityEditor.CoreModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.DeviceSimulatorModule">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\Managed\UnityEngine\UnityEditor.DeviceSimulatorModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.DiagnosticsModule">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\Managed\UnityEngine\UnityEditor.DiagnosticsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.EditorToolbarModule">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\Managed\UnityEngine\UnityEditor.EditorToolbarModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.GraphViewModule">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\Managed\UnityEngine\UnityEditor.GraphViewModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.PresetsUIModule">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\Managed\UnityEngine\UnityEditor.PresetsUIModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.QuickSearchModule">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\Managed\UnityEngine\UnityEditor.QuickSearchModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.SceneTemplateModule">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\Managed\UnityEngine\UnityEditor.SceneTemplateModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.SceneViewModule">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\Managed\UnityEngine\UnityEditor.SceneViewModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.TextCoreFontEngineModule">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\Managed\UnityEngine\UnityEditor.TextCoreFontEngineModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.TextCoreTextEngineModule">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\Managed\UnityEngine\UnityEditor.TextCoreTextEngineModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UIBuilderModule">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\Managed\UnityEngine\UnityEditor.UIBuilderModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UIElementsModule">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\Managed\UnityEngine\UnityEditor.UIElementsModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UIElementsSamplesModule">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\Managed\UnityEngine\UnityEditor.UIElementsSamplesModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UnityConnectModule">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\Managed\UnityEngine\UnityEditor.UnityConnectModule.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.Graphs">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\Managed\UnityEditor.Graphs.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.Android.Extensions">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\PlaybackEngines\AndroidPlayer\UnityEditor.Android.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.WindowsStandalone.Extensions">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\PlaybackEngines\WindowsStandaloneSupport\UnityEditor.WindowsStandalone.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="DemiEditor">
      <HintPath>Assets\Plugins\Demigiant\DemiLib\Core\Editor\DemiEditor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="MessagePack.Annotations">
      <HintPath>Assets\Packages\MessagePack.Annotations.3.1.3\lib\netstandard2.0\MessagePack.Annotations.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Collections.Immutable">
      <HintPath>Assets\Packages\System.Collections.Immutable.8.0.0\lib\netstandard2.0\System.Collections.Immutable.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Collections.LowLevel.ILSupport">
      <HintPath>Library\PackageCache\com.unity.collections@2.5.1\Unity.Collections.LowLevel.ILSupport\Unity.Collections.LowLevel.ILSupport.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="nunit.framework">
      <HintPath>Library\PackageCache\com.unity.ext.nunit@2.0.3\net40\unity-custom\nunit.framework.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Sirenix.Serialization">
      <HintPath>Assets\Plugins\Sirenix\Assemblies\Sirenix.Serialization.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="MessagePack">
      <HintPath>Assets\Packages\MessagePack.3.1.3\lib\netstandard2.1\MessagePack.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="DemiLib">
      <HintPath>Assets\Plugins\Demigiant\DemiLib\Core\DemiLib.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Sirenix.Serialization.Config">
      <HintPath>Assets\Plugins\Sirenix\Assemblies\Sirenix.Serialization.Config.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Sirenix.OdinInspector.Editor">
      <HintPath>Assets\Plugins\Sirenix\Assemblies\Sirenix.OdinInspector.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="DOTweenEditor">
      <HintPath>Assets\Plugins\Demigiant\DOTween\Editor\DOTweenEditor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="DOTweenProEditor">
      <HintPath>Assets\Plugins\Demigiant\DOTweenPro\Editor\DOTweenProEditor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.CompilerServices.Unsafe">
      <HintPath>Assets\LiveScriptReload\Plugins\FastScriptReload\Plugins\Roslyn\2021+\System.Runtime.CompilerServices.Unsafe.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Sirenix.OdinInspector.Attributes">
      <HintPath>Assets\Plugins\Sirenix\Assemblies\Sirenix.OdinInspector.Attributes.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Sirenix.Utilities">
      <HintPath>Assets\Plugins\Sirenix\Assemblies\Sirenix.Utilities.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Google.Protobuf">
      <HintPath>Assets\Plugins\Protobuf\Google.Protobuf.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="DOTween">
      <HintPath>Assets\Plugins\Demigiant\DOTween\DOTween.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="dnlib">
      <HintPath>Library\PackageCache\com.code-philosophy.hybridclr@59a3c3974a\Plugins\dnlib.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="DOTweenPro">
      <HintPath>Assets\Plugins\Demigiant\DOTweenPro\DOTweenPro.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="LZ4">
      <HintPath>Library\PackageCache\com.code-philosophy.hybridclr@59a3c3974a\Plugins\LZ4.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.NET.StringTools">
      <HintPath>Assets\Packages\Microsoft.NET.StringTools.17.11.4\lib\netstandard2.0\Microsoft.NET.StringTools.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.iOS_I2Loc.Xcode">
      <HintPath>Assets\TEngine\Editor\Localization\Unity XCode\UnityEditor.iOS_I2Loc.Xcode.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="NuGetForUnity.PluginAPI">
      <HintPath>Assets\NuGet\Editor\NuGetForUnity.PluginAPI.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Sirenix.Utilities.Editor">
      <HintPath>Assets\Plugins\Sirenix\Assemblies\Sirenix.Utilities.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Newtonsoft.Json">
      <HintPath>Library\PackageCache\com.unity.nuget.newtonsoft-json@3.2.1\Runtime\Newtonsoft.Json.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Mono.Cecil">
      <HintPath>Library\PackageCache\com.unity.nuget.mono-cecil@1.11.4\Mono.Cecil.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="NugetForUnity">
      <HintPath>Assets\NuGet\Editor\NugetForUnity.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="NuGet.Frameworks">
      <HintPath>Assets\Plugins\Protobuf\NuGet.Frameworks.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Sirenix.OdinValidator.Editor">
      <HintPath>Assets\Plugins\Sirenix\Assemblies\Sirenix.OdinValidator.Editor.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Android.Types">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\PlaybackEngines\AndroidPlayer\Unity.Android.Types.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Android.Gradle">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\PlaybackEngines\AndroidPlayer\Unity.Android.Gradle.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.Android.GradleProject">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\PlaybackEngines\AndroidPlayer\Unity.Android.GradleProject.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="mscorlib">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\mscorlib.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Core">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Core.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Runtime.Serialization.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Xml.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.Linq">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Xml.Linq.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Numerics">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Numerics.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Numerics.Vectors">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Numerics.Vectors.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Http">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Net.Http.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.Compression">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.IO.Compression.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.CSharp">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Microsoft.CSharp.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Data">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Data.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Data.DataSetExtensions">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Data.DataSetExtensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Drawing">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Drawing.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.Compression.FileSystem">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.IO.Compression.FileSystem.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel.Composition">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.ComponentModel.Composition.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Transactions">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\System.Transactions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Microsoft.Win32.Primitives">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\Microsoft.Win32.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="netstandard">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\netstandard.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.AppContext">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.AppContext.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Buffers">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Buffers.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Collections.Concurrent">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Collections.Concurrent.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Collections">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Collections.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Collections.NonGeneric">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Collections.NonGeneric.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Collections.Specialized">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Collections.Specialized.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel.Annotations">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ComponentModel.Annotations.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ComponentModel.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel.EventBasedAsync">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ComponentModel.EventBasedAsync.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel.Primitives">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ComponentModel.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ComponentModel.TypeConverter">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ComponentModel.TypeConverter.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Console">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Console.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Data.Common">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Data.Common.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.Contracts">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Diagnostics.Contracts.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.Debug">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Diagnostics.Debug.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.FileVersionInfo">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Diagnostics.FileVersionInfo.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.Process">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Diagnostics.Process.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.StackTrace">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Diagnostics.StackTrace.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.TextWriterTraceListener">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Diagnostics.TextWriterTraceListener.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.Tools">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Diagnostics.Tools.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Diagnostics.TraceSource">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Diagnostics.TraceSource.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Drawing.Primitives">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Drawing.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Dynamic.Runtime">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Dynamic.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Globalization.Calendars">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Globalization.Calendars.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Globalization">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Globalization.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Globalization.Extensions">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Globalization.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.Compression.ZipFile">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.Compression.ZipFile.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.FileSystem">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.FileSystem.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.FileSystem.DriveInfo">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.FileSystem.DriveInfo.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.FileSystem.Primitives">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.FileSystem.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.FileSystem.Watcher">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.FileSystem.Watcher.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.IsolatedStorage">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.IsolatedStorage.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.MemoryMappedFiles">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.MemoryMappedFiles.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.Pipes">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.Pipes.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.IO.UnmanagedMemoryStream">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.IO.UnmanagedMemoryStream.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Linq">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Linq.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Linq.Expressions">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Linq.Expressions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Linq.Parallel">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Linq.Parallel.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Linq.Queryable">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Linq.Queryable.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Memory">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Memory.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Http.Rtc">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.Http.Rtc.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.NameResolution">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.NameResolution.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.NetworkInformation">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.NetworkInformation.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Ping">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.Ping.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Primitives">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Requests">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.Requests.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Security">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.Security.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Sockets">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.Sockets.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.WebHeaderCollection">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.WebHeaderCollection.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.WebSockets.Client">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.WebSockets.Client.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.WebSockets">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Net.WebSockets.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ObjectModel">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ObjectModel.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.DispatchProxy">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Reflection.DispatchProxy.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Reflection.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.Emit">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Reflection.Emit.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.Emit.ILGeneration">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Reflection.Emit.ILGeneration.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.Emit.Lightweight">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Reflection.Emit.Lightweight.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.Extensions">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Reflection.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Reflection.Primitives">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Reflection.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Resources.Reader">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Resources.Reader.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Resources.ResourceManager">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Resources.ResourceManager.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Resources.Writer">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Resources.Writer.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.CompilerServices.VisualC">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.CompilerServices.VisualC.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Extensions">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Handles">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.Handles.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.InteropServices">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.InteropServices.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.InteropServices.RuntimeInformation">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.InteropServices.RuntimeInformation.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.InteropServices.WindowsRuntime">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.InteropServices.WindowsRuntime.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Numerics">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.Numerics.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Formatters">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.Serialization.Formatters.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Json">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.Serialization.Json.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Primitives">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.Serialization.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Runtime.Serialization.Xml">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Runtime.Serialization.Xml.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Claims">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Security.Claims.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Algorithms">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Security.Cryptography.Algorithms.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Csp">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Security.Cryptography.Csp.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Encoding">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Security.Cryptography.Encoding.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Primitives">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Security.Cryptography.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.X509Certificates">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Security.Cryptography.X509Certificates.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.Principal">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Security.Principal.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Security.SecureString">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Security.SecureString.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ServiceModel.Duplex">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ServiceModel.Duplex.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ServiceModel.Http">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ServiceModel.Http.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ServiceModel.NetTcp">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ServiceModel.NetTcp.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ServiceModel.Primitives">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ServiceModel.Primitives.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ServiceModel.Security">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ServiceModel.Security.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Text.Encoding">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Text.Encoding.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Text.Encoding.Extensions">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Text.Encoding.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Text.RegularExpressions">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Text.RegularExpressions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Threading.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Overlapped">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Threading.Overlapped.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Tasks">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Threading.Tasks.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Tasks.Extensions">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Threading.Tasks.Extensions.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Tasks.Parallel">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Threading.Tasks.Parallel.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Thread">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Threading.Thread.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.ThreadPool">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Threading.ThreadPool.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Threading.Timer">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Threading.Timer.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.ValueTuple">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.ValueTuple.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.ReaderWriter">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Xml.ReaderWriter.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.XDocument">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Xml.XDocument.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.XmlDocument">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Xml.XmlDocument.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.XmlSerializer">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Xml.XmlSerializer.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.XPath">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Xml.XPath.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.XPath.XDocument">
      <HintPath>D:\Softs\Unity Editor\2022.3.51f1c1\Editor\Data\UnityReferenceAssemblies\unity-4.8-api\Facades\System.Xml.XPath.XDocument.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.TextMeshPro">
      <HintPath>Library\ScriptAssemblies\Unity.TextMeshPro.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.AI.Navigation">
      <HintPath>Library\ScriptAssemblies\Unity.AI.Navigation.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Cinemachine">
      <HintPath>Library\ScriptAssemblies\Cinemachine.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Unity.InputSystem">
      <HintPath>Library\ScriptAssemblies\Unity.InputSystem.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEditor.UI">
      <HintPath>Library\ScriptAssemblies\UnityEditor.UI.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="UnityEngine.UI">
      <HintPath>Library\ScriptAssemblies\UnityEngine.UI.dll</HintPath>
      <Private>False</Private>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="TEngine.Runtime.csproj" />
    <ProjectReference Include="GameProto.csproj" />
    <ProjectReference Include="GameBase.csproj" />
    <ProjectReference Include="GameCommon.csproj" />
    <ProjectReference Include="UniTask.csproj" />
    <ProjectReference Include="YooAsset.csproj" />
    <ProjectReference Include="DG.Tweening.csproj" />
    <ProjectReference Include="PhysicsTankMaker.csproj" />
    <ProjectReference Include="JUTPS.csproj" />
  </ItemGroup>
  <Import Project="Sdk.targets" Sdk="Microsoft.NET.Sdk" />
  <ItemGroup>
    <ProjectCapability Remove="LaunchProfiles" />
    <ProjectCapability Remove="SharedProjectReferences" />
    <ProjectCapability Remove="ReferenceManagerSharedProjects" />
    <ProjectCapability Remove="ProjectReferences" />
    <ProjectCapability Remove="ReferenceManagerProjects" />
    <ProjectCapability Remove="COMReferences" />
    <ProjectCapability Remove="ReferenceManagerCOM" />
    <ProjectCapability Remove="AssemblyReferences" />
    <ProjectCapability Remove="ReferenceManagerAssemblies" />
  </ItemGroup>
</Project>
