using UnityEngine.UI;
using TEngine;
using DG.Tweening;

namespace GameLogic
{
    [Window(UILayer.UI)]
    class UILoading : UIWindow
    {
        #region 脚本工具生成的代码
        private Image m_imgBar;
        DG.Tweening.Core.TweenerCore<float, float, DG.Tweening.Plugins.Options.FloatOptions> m_tween;
        float completeTime = 5.0f;
        protected override void ScriptGenerator()
        {
            m_imgBar = FindChildComponent<Image>("t1/m_imgBar");
        }
        #endregion

        protected override void OnCreate()
        {
            base.OnCreate();
            completeTime = (float)this.userDatas[0];
            m_imgBar.fillAmount = 0;
            m_tween = DOTween.To(() => m_imgBar.fillAmount, x => m_imgBar.fillAmount = x, 1, completeTime).OnComplete(() => this.Close());
            this.AddUIEvent<int>(GameCommon.ActorEventDefine.Loading, OnLoadingState);
        }

        #region 事件
        private void OnLoadingState(int _state)
        {
            if (_state == 1)
            {
                m_tween.Kill();
                m_imgBar.fillAmount = 1;
                DOVirtual.DelayedCall(0.5f, () =>
                {
                    GameModule.UI.CloseUI<UILoading>();
                });
            }
        }
        #endregion


    }
}
