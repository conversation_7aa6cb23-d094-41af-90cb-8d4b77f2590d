{"format": 1, "restore": {"D:\\Work\\military-zone-client\\mz-client\\DOTweenPro-Exclude.csproj": {}}, "projects": {"D:\\Work\\military-zone-client\\mz-client\\DG.Tweening.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Work\\military-zone-client\\mz-client\\DG.Tweening.csproj", "projectName": "<PERSON><PERSON><PERSON>T<PERSON>", "projectPath": "D:\\Work\\military-zone-client\\mz-client\\DG.Tweening.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Work\\military-zone-client\\mz-client\\Temp\\obj\\Debug\\DG.Tweening\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.104\\RuntimeIdentifierGraph.json"}}}, "D:\\Work\\military-zone-client\\mz-client\\DOTweenPro-Exclude.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Work\\military-zone-client\\mz-client\\DOTweenPro-Exclude.csproj", "projectName": "DOTweenPro-Exclude", "projectPath": "D:\\Work\\military-zone-client\\mz-client\\DOTweenPro-Exclude.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Work\\military-zone-client\\mz-client\\Temp\\obj\\Debug\\DOTweenPro-Exclude\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Work\\military-zone-client\\mz-client\\DG.Tweening.csproj": {"projectPath": "D:\\Work\\military-zone-client\\mz-client\\DG.Tweening.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.104\\RuntimeIdentifierGraph.json"}}}}}