{"version": 3, "targets": {".NETStandard,Version=v2.1": {"EPO/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"EPOUtilities": "1.0.0"}, "compile": {"bin/placeholder/EPO.dll": {}}, "runtime": {"bin/placeholder/EPO.dll": {}}}, "EPOHDRP/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"EPO": "1.0.0"}, "compile": {"bin/placeholder/EPOHDRP.dll": {}}, "runtime": {"bin/placeholder/EPOHDRP.dll": {}}}, "EPOURP/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"EPO": "1.0.0"}, "compile": {"bin/placeholder/EPOURP.dll": {}}, "runtime": {"bin/placeholder/EPOURP.dll": {}}}, "EPOUtilities/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "compile": {"bin/placeholder/EPOUtilities.dll": {}}, "runtime": {"bin/placeholder/EPOUtilities.dll": {}}}}}, "libraries": {"EPO/1.0.0": {"type": "project", "path": "EPO.csproj", "msbuildProject": "EPO.csproj"}, "EPOHDRP/1.0.0": {"type": "project", "path": "EPOHDRP.csproj", "msbuildProject": "EPOHDRP.csproj"}, "EPOURP/1.0.0": {"type": "project", "path": "EPOURP.csproj", "msbuildProject": "EPOURP.csproj"}, "EPOUtilities/1.0.0": {"type": "project", "path": "EPOUtilities.csproj", "msbuildProject": "EPOUtilities.csproj"}}, "projectFileDependencyGroups": {".NETStandard,Version=v2.1": ["EPO >= 1.0.0", "EPOHDRP >= 1.0.0", "EPOURP >= 1.0.0", "EPOUtilities >= 1.0.0"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Work\\military-zone-client\\mz-client\\EPOEditor.csproj", "projectName": "EPOEditor", "projectPath": "D:\\Work\\military-zone-client\\mz-client\\EPOEditor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Work\\military-zone-client\\mz-client\\Temp\\obj\\Debug\\EPOEditor\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Work\\military-zone-client\\mz-client\\EPO.csproj": {"projectPath": "D:\\Work\\military-zone-client\\mz-client\\EPO.csproj"}, "D:\\Work\\military-zone-client\\mz-client\\EPOHDRP.csproj": {"projectPath": "D:\\Work\\military-zone-client\\mz-client\\EPOHDRP.csproj"}, "D:\\Work\\military-zone-client\\mz-client\\EPOURP.csproj": {"projectPath": "D:\\Work\\military-zone-client\\mz-client\\EPOURP.csproj"}, "D:\\Work\\military-zone-client\\mz-client\\EPOUtilities.csproj": {"projectPath": "D:\\Work\\military-zone-client\\mz-client\\EPOUtilities.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.104\\RuntimeIdentifierGraph.json"}}}}