using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Sockets;
using System.Reflection;
using System.Threading.Tasks;
using Google.Protobuf;
using Message;
using TEngine;
using UnityEngine;

namespace GameBase
{
    public class PacketItem
    {
        public uint msgId;
        public object msg;

        public PacketItem(uint id, object msg)
        {
            msgId = id;
            this.msg = msg;
        }

        public PacketItem(object msg)
        {
            this.msg = msg;
            msgId = 0;
        }

        public PacketItem(uint id)
        {
            msg = null;
            msgId = id;
        }
    }

    public class NetModule : MonoBehaviour
    {
        private System.Net.Sockets.Socket _client;
        private System.Net.IPEndPoint EndPoint { get; set; }
        public static readonly uint ConnectedEvent = NetPacket.GetPacketId("OnNetworkConnected".ToLower());
        public static readonly uint DisConnectedEvent = NetPacket.GetPacketId("OnNetworkDisconnected".ToLower());
        public float HearBeatTime = 10f;
        public float ReconnectInterval = 3f;
        public int ReconnectTimes = 5;
        public bool IsConnected { get; set; }
        public bool IsAutoReconnect = true;

        private object _recvLock = new object();
        private object _sendLock = new object();
        protected List<object> _consumer = new List<object>();
        protected List<PacketItem> _receiveQueue = new List<PacketItem>();
        protected List<PacketItem> _sendQueue = new List<PacketItem>();
        protected string host;
        protected int port;
        protected NetPacketParser packetParser = new NetPacketParser();
        protected byte[] _receiveBuffer = new byte[4096];

        private int reconnectCount = 0;
        private bool isReconnecting = false;
        private bool shouldStartReconnect = false; // 添加重连标志位

        protected virtual void Awake()
        {
            NetPacket.RegisterPacket("OnNetworkConnected");
            NetPacket.RegisterPacket("OnNetworkDisconnected");
        }

        public async void Connect(string host, int port)
        {
            this.host = host;
            this.port = port;
            var entry = System.Net.Dns.GetHostEntry(host);
            if (entry.AddressList.Length <= 0)
            {
                return;
            }
            Log.Info(string.Format("服务器地址:{0}", entry.AddressList));
            EndPoint = new System.Net.IPEndPoint(entry.AddressList[0], port);
            _client = new System.Net.Sockets.Socket(AddressFamily.InterNetwork, SocketType.Stream, ProtocolType.Tcp);

            try
            {
                await _client.ConnectAsync(EndPoint);
            }
            catch (SocketException ex)
            {
                Log.Error("连接失败:" + ex.Message);
                return;
            }

            onConnected(_client.Connected);
        }

        public virtual void Send(object msg)
        {
            if (!IsConnected) return;
            lock (_sendLock)
            {
                _sendQueue.Add(new PacketItem(msg));
            }
        }

        private const int Default_Ipacket_Stx = 0x27;
        private const int Default_Ipacket_Ckx = 0x72;

        public static Ipacket BuildPacketHead(long id, SERVICE destservertype = SERVICE.Gate)
        {
            var packetHead = new Ipacket();
            packetHead.Ckx = Default_Ipacket_Ckx;
            packetHead.Stx = Default_Ipacket_Stx;
            packetHead.Id = id;
            packetHead.DestServerType = destservertype;

            return packetHead;
        }

        public void AddNetworkEvent(object targeter)
        {
            _consumer.Add(targeter);
        }

        public void RemoveNetworkEvent(object targeter)
        {
            _consumer.Remove(targeter);
        }


        protected void Update()
        {
            // 检查是否需要启动重连
            if (shouldStartReconnect && !isReconnecting)
            {
                shouldStartReconnect = false;
                isReconnecting = true;
                InvokeRepeating("onReconnect", ReconnectInterval, ReconnectInterval);
                CancelInvoke("onHeartbeat");
            }

            while (_sendQueue.Count > 0)
            {
                var item = _sendQueue.First();
                _sendQueue.Remove(item);
                onSend(item);
            }

            List<PacketItem> items = new List<PacketItem>();
            lock (_recvLock)
            {
                items.AddRange(_receiveQueue);
                _receiveQueue.Clear();
            }

            for (int i = 0; i < items.Count; i++)
            {
                var data = items[i];
                var name = NetPacket.GetPacketName(data.msgId);
                for (var j = 0; j < _consumer.Count; j++)
                {
                    var c = _consumer[j];
                    var info = c.GetType().GetMethod(name, BindingFlags.Instance | BindingFlags.NonPublic | BindingFlags.Public);
                    if (info == null) continue;
                    Log.Info("收到 ：" + name);
                    object[] parameters = new object[] { data.msg };
                    try
                    {
                        info.Invoke(c, parameters);
                    }
                    catch (ArgumentException ae)
                    {
                        Log.Warning("调用[" + name + "]方法错误:" + ae.Message);
                        continue;
                    }
                }
            }
        }

        protected void InputBuffer(byte[] buffer, int len)
        {
            var buffers = packetParser.Read(buffer.Skip(0).Take(len).ToArray());
            for (int i = 0; i < buffers.Count; ++i)
            {
                var buf = buffers[i];
                var packetId = NetPacket.ReadId(buf);
                var body = buffers[i].Skip(4).Take(buf.Length - 4).ToArray();

                var packet = NetPacket.GetPacket(packetId);
                if (packet == null)
                {
                    Log.Warning("[Network]:无效数据包");
                    break;
                }

                IMessage data;
                try
                {
                    data = packet.ParseFrom(body);
                }
                catch (Exception e)
                {
                    Log.Error("[Socket Network]:数据包解析失败:" + e.Message);
                    continue;
                }

                // 加入队列
                lock (_recvLock)
                {
                    _receiveQueue.Add(new PacketItem(packetId, data));
                }

            }
        }

        protected void onSend(PacketItem item)
        {
            if (item.msg == null && item.msgId == 0) return;

            if (!IsConnected)
            {
                Log.Error("[Network]: 还没有连接服务器，发送失败");
                return;
            }

            var body = new byte[0];
            var id = item.msgId;
            if (item.msg != null)
            {
                IMessage msg = item.msg as IMessage;
                id = NetPacket.GetPacketId(msg.Descriptor.Name.ToLower());
                body = msg.ToByteArray();
            }

            if (!BitConverter.IsLittleEndian)
            {
                Array.Reverse(body);
            }

            NetPacket.WriteId(ref body, id);
            NetPacket.WriteLen(ref body, (uint)body.Length);

            doSend(body);
        }

        protected void doThreadUpdate()
        {

        }

        protected void dispatchNetworkEvent(PacketItem item)
        {
            // 加入队列
            lock (_recvLock)
            {
                _receiveQueue.Add(item);
            }

        }

        protected void dispatchNetworkEvent(uint id, object data)
        {
            dispatchNetworkEvent(new PacketItem(id, data));
        }


        protected void SendHearbeat(uint msgId)
        {
            if (!IsConnected) return;
            lock (_sendLock)
            {
                _sendQueue.Add(new PacketItem(msgId));
            }
        }

        protected void onConnected(bool connected)
        {
            IsConnected = connected;
            if (IsConnected)
            {
                // 启动心跳定时器
                InvokeRepeating("onHeartbeat", HearBeatTime, HearBeatTime);

                // 启动接收线程
                Task.Run(() =>
                {
                    while (IsConnected)
                    {
                        int len = doReceive();
                        if (len < 0)
                        {
                            Log.Error("读取网络数据错误: len = " + len);
                            IsConnected = false;

                            dispatchNetworkEvent(DisConnectedEvent, null);

                            // 断线重连
                            if (IsAutoReconnect && !isReconnecting)
                            {
                                Log.Warning("触发断线重连");
                                shouldStartReconnect = true;
                            }
                            break;
                        }
                        InputBuffer(_receiveBuffer, len);
                        doThreadUpdate();
                    }
                    Log.Info("接收线程结束");
                });

                dispatchNetworkEvent(ConnectedEvent, isReconnecting);

                // 重置断线重连
                isReconnecting = false;
                IsAutoReconnect = true;
                reconnectCount = 0;
                CancelInvoke("onReconnect");
            }
            if (connected)
                SendHearbeat(NetPacket.Hearbeat);
        }

        protected void onReconnect()
        {
            reconnectCount++;
            if (reconnectCount <= ReconnectTimes)
            {
                doReconnect();
            }
            else
            {
                // 关闭自动重连
                IsAutoReconnect = false;
                isReconnecting = false;
                // 取消定时器
                CancelInvoke("onReconnect");
            }
        }

        protected void doSend(byte[] buffer)
        {
            _client.SendAsync(new ArraySegment<byte>(buffer), SocketFlags.None);
        }

        protected int doReceive()
        {
            int len = -1;
            try
            {
                len = _client.Receive(_receiveBuffer);
            }
            catch (SocketException ex)
            {
                Log.Error(ex.Message);
            }

            return len;
        }

        protected async void doReconnect()
        {
            if (_client != null)
            {
                Connect(host, port);
                return;
            }

            await _client.ConnectAsync(EndPoint);
            onConnected(_client.Connected);
        }

        protected void onHeartbeat()
        {
            SendHearbeat(NetPacket.Hearbeat);
        }

        protected void OnDestroy()
        {
            IsConnected = false;
            IsAutoReconnect = false;
            CancelInvoke();
            Log.Info("NetWork OnDestroy");
            if (_client != null)
            {
                if (_client.Connected)
                {
                    SocketAsyncEventArgs args = new SocketAsyncEventArgs();
                    args.DisconnectReuseSocket = true;
                    args.Completed += (object sender, SocketAsyncEventArgs e) =>
                    {
                        if (SocketError.Success == e.SocketError)
                        {
                        }
                        //_client.Dispose();
                    };

                    _client.DisconnectAsync(args);
                    return;
                }
                //_client.Dispose();
            }
        }
    }
}
