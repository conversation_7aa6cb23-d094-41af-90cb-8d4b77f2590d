{"version": 3, "targets": {".NETStandard,Version=v2.1": {"DG.Tweening/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "compile": {"bin/placeholder/DG.Tweening.dll": {}}, "runtime": {"bin/placeholder/DG.Tweening.dll": {}}}, "GameBase/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"GameProto": "1.0.0", "TEngine.Runtime": "1.0.0", "UniTask": "1.0.0"}, "compile": {"bin/placeholder/GameBase.dll": {}}, "runtime": {"bin/placeholder/GameBase.dll": {}}}, "GameCommon/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"GameBase": "1.0.0", "GameProto": "1.0.0", "TEngine.Runtime": "1.0.0"}, "compile": {"bin/placeholder/GameCommon.dll": {}}, "runtime": {"bin/placeholder/GameCommon.dll": {}}}, "GameLogic/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"DG.Tweening": "1.0.0", "GameBase": "1.0.0", "GameCommon": "1.0.0", "GameProto": "1.0.0", "JUTPS": "1.0.0", "PhysicsTankMaker": "1.0.0", "TEngine.Runtime": "1.0.0", "UniTask": "1.0.0", "YooAsset": "1.0.0"}, "compile": {"bin/placeholder/GameLogic.dll": {}}, "runtime": {"bin/placeholder/GameLogic.dll": {}}}, "GameProto/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "compile": {"bin/placeholder/GameProto.dll": {}}, "runtime": {"bin/placeholder/GameProto.dll": {}}}, "JUTPS/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"GameCommon": "1.0.0"}, "compile": {"bin/placeholder/JUTPS.dll": {}}, "runtime": {"bin/placeholder/JUTPS.dll": {}}}, "PhysicsTankMaker/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"GameCommon": "1.0.0", "TEngine.Runtime": "1.0.0"}, "compile": {"bin/placeholder/PhysicsTankMaker.dll": {}}, "runtime": {"bin/placeholder/PhysicsTankMaker.dll": {}}}, "TEngine.Runtime/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"UniTask": "1.0.0", "YooAsset": "1.0.0"}, "compile": {"bin/placeholder/TEngine.Runtime.dll": {}}, "runtime": {"bin/placeholder/TEngine.Runtime.dll": {}}}, "UniTask/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"YooAsset": "1.0.0"}, "compile": {"bin/placeholder/UniTask.dll": {}}, "runtime": {"bin/placeholder/UniTask.dll": {}}}, "YooAsset/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "compile": {"bin/placeholder/YooAsset.dll": {}}, "runtime": {"bin/placeholder/YooAsset.dll": {}}}, "YooAsset.Editor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"YooAsset": "1.0.0"}, "compile": {"bin/placeholder/YooAsset.Editor.dll": {}}, "runtime": {"bin/placeholder/YooAsset.Editor.dll": {}}}}}, "libraries": {"DG.Tweening/1.0.0": {"type": "project", "path": "DG.Tweening.csproj", "msbuildProject": "DG.Tweening.csproj"}, "GameBase/1.0.0": {"type": "project", "path": "GameBase.csproj", "msbuildProject": "GameBase.csproj"}, "GameCommon/1.0.0": {"type": "project", "path": "GameCommon.csproj", "msbuildProject": "GameCommon.csproj"}, "GameLogic/1.0.0": {"type": "project", "path": "GameLogic.csproj", "msbuildProject": "GameLogic.csproj"}, "GameProto/1.0.0": {"type": "project", "path": "GameProto.csproj", "msbuildProject": "GameProto.csproj"}, "JUTPS/1.0.0": {"type": "project", "path": "JUTPS.csproj", "msbuildProject": "JUTPS.csproj"}, "PhysicsTankMaker/1.0.0": {"type": "project", "path": "PhysicsTankMaker.csproj", "msbuildProject": "PhysicsTankMaker.csproj"}, "TEngine.Runtime/1.0.0": {"type": "project", "path": "TEngine.Runtime.csproj", "msbuildProject": "TEngine.Runtime.csproj"}, "UniTask/1.0.0": {"type": "project", "path": "UniTask.csproj", "msbuildProject": "UniTask.csproj"}, "YooAsset/1.0.0": {"type": "project", "path": "YooAsset.csproj", "msbuildProject": "YooAsset.csproj"}, "YooAsset.Editor/1.0.0": {"type": "project", "path": "YooAsset.Editor.csproj", "msbuildProject": "YooAsset.Editor.csproj"}}, "projectFileDependencyGroups": {".NETStandard,Version=v2.1": ["GameLogic >= 1.0.0", "TEngine.Runtime >= 1.0.0", "YooAsset >= 1.0.0", "YooAsset.Editor >= 1.0.0"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Work\\military-zone-client\\mz-client\\TEngine.Editor.csproj", "projectName": "TEngine.Editor", "projectPath": "D:\\Work\\military-zone-client\\mz-client\\TEngine.Editor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Work\\military-zone-client\\mz-client\\Temp\\obj\\Debug\\TEngine.Editor\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\Work\\military-zone-client\\mz-client\\GameLogic.csproj": {"projectPath": "D:\\Work\\military-zone-client\\mz-client\\GameLogic.csproj"}, "D:\\Work\\military-zone-client\\mz-client\\TEngine.Runtime.csproj": {"projectPath": "D:\\Work\\military-zone-client\\mz-client\\TEngine.Runtime.csproj"}, "D:\\Work\\military-zone-client\\mz-client\\YooAsset.csproj": {"projectPath": "D:\\Work\\military-zone-client\\mz-client\\YooAsset.csproj"}, "D:\\Work\\military-zone-client\\mz-client\\YooAsset.Editor.csproj": {"projectPath": "D:\\Work\\military-zone-client\\mz-client\\YooAsset.Editor.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.104\\RuntimeIdentifierGraph.json"}}}}