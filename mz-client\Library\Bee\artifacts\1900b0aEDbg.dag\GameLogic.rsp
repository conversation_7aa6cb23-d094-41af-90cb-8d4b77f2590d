-target:library
-out:"Library/Bee/artifacts/1900b0aEDbg.dag/GameLogic.dll"
-refout:"Library/Bee/artifacts/1900b0aEDbg.dag/GameLogic.ref.dll"
-define:UNITY_2022_3_51
-define:UNITY_2022_3
-define:UNITY_2022
-define:UNITY_5_3_OR_NEWER
-define:UNITY_5_4_OR_NEWER
-define:UNITY_5_5_OR_NEWER
-define:UNITY_5_6_OR_NEWER
-define:UNITY_2017_1_OR_NEWER
-define:UNITY_2017_2_OR_NEWER
-define:UNITY_2017_3_OR_NEWER
-define:UNITY_2017_4_OR_NEWER
-define:UNITY_2018_1_OR_NEWER
-define:UNITY_2018_2_OR_NEWER
-define:UNITY_2018_3_OR_NEWER
-define:UNITY_2018_4_OR_NEWER
-define:UNITY_2019_1_OR_NEWER
-define:UNITY_2019_2_OR_NEWER
-define:UNITY_2019_3_OR_NEWER
-define:UNITY_2019_4_OR_NEWER
-define:UNITY_2020_1_OR_NEWER
-define:UNITY_2020_2_OR_NEWER
-define:UNITY_2020_3_OR_NEWER
-define:UNITY_2021_1_OR_NEWER
-define:UNITY_2021_2_OR_NEWER
-define:UNITY_2021_3_OR_NEWER
-define:UNITY_2022_1_OR_NEWER
-define:UNITY_2022_2_OR_NEWER
-define:UNITY_2022_3_OR_NEWER
-define:PLATFORM_ARCH_64
-define:UNITY_64
-define:UNITY_INCLUDE_TESTS
-define:ENABLE_AR
-define:ENABLE_AUDIO
-define:ENABLE_CACHING
-define:ENABLE_CLOTH
-define:ENABLE_EVENT_QUEUE
-define:ENABLE_MICROPHONE
-define:ENABLE_MULTIPLE_DISPLAYS
-define:ENABLE_PHYSICS
-define:ENABLE_TEXTURE_STREAMING
-define:ENABLE_VIRTUALTEXTURING
-define:ENABLE_LZMA
-define:ENABLE_UNITYEVENTS
-define:ENABLE_VR
-define:ENABLE_WEBCAM
-define:ENABLE_UNITYWEBREQUEST
-define:ENABLE_WWW
-define:ENABLE_CLOUD_SERVICES
-define:ENABLE_CLOUD_SERVICES_ADS
-define:ENABLE_CLOUD_SERVICES_USE_WEBREQUEST
-define:ENABLE_CLOUD_SERVICES_CRASH_REPORTING
-define:ENABLE_CLOUD_SERVICES_PURCHASING
-define:ENABLE_CLOUD_SERVICES_ANALYTICS
-define:ENABLE_CLOUD_SERVICES_BUILD
-define:ENABLE_EDITOR_GAME_SERVICES
-define:ENABLE_UNITY_GAME_SERVICES_ANALYTICS_SUPPORT
-define:ENABLE_CLOUD_LICENSE
-define:ENABLE_EDITOR_HUB_LICENSE
-define:ENABLE_WEBSOCKET_CLIENT
-define:ENABLE_GENERATE_NATIVE_PLUGINS_FOR_ASSEMBLIES_API
-define:ENABLE_DIRECTOR_AUDIO
-define:ENABLE_DIRECTOR_TEXTURE
-define:ENABLE_MANAGED_JOBS
-define:ENABLE_MANAGED_TRANSFORM_JOBS
-define:ENABLE_MANAGED_ANIMATION_JOBS
-define:ENABLE_MANAGED_AUDIO_JOBS
-define:ENABLE_MANAGED_UNITYTLS
-define:INCLUDE_DYNAMIC_GI
-define:ENABLE_SCRIPTING_GC_WBARRIERS
-define:PLATFORM_SUPPORTS_MONO
-define:RENDER_SOFTWARE_CURSOR
-define:ENABLE_VIDEO
-define:ENABLE_ACCELERATOR_CLIENT_DEBUGGING
-define:ENABLE_NAVIGATION_PACKAGE_DEBUG_VISUALIZATION
-define:ENABLE_NAVIGATION_HEIGHTMESH_RUNTIME_SUPPORT
-define:ENABLE_NAVIGATION_UI_REQUIRES_PACKAGE
-define:PLATFORM_STANDALONE
-define:TEXTCORE_1_0_OR_NEWER
-define:PLATFORM_STANDALONE_WIN
-define:UNITY_STANDALONE_WIN
-define:UNITY_STANDALONE
-define:UNITY_UGP_API
-define:ENABLE_RUNTIME_GI
-define:ENABLE_MOVIES
-define:ENABLE_NETWORK
-define:ENABLE_NVIDIA
-define:ENABLE_CRUNCH_TEXTURE_COMPRESSION
-define:ENABLE_OUT_OF_PROCESS_CRASH_HANDLER
-define:ENABLE_CLUSTER_SYNC
-define:ENABLE_CLUSTERINPUT
-define:PLATFORM_UPDATES_TIME_OUTSIDE_OF_PLAYER_LOOP
-define:GFXDEVICE_WAITFOREVENT_MESSAGEPUMP
-define:PLATFORM_INITIALIZES_MEMORY_MANAGER_EXPLICITLY
-define:ENABLE_MONO
-define:NET_4_6
-define:NET_UNITY_4_8
-define:ENABLE_PROFILER
-define:DEBUG
-define:TRACE
-define:UNITY_ASSERTIONS
-define:UNITY_EDITOR
-define:UNITY_EDITOR_64
-define:UNITY_EDITOR_WIN
-define:ENABLE_UNITY_COLLECTIONS_CHECKS
-define:ENABLE_BURST_AOT
-define:UNITY_TEAM_LICENSE
-define:ENABLE_CUSTOM_RENDER_TEXTURE
-define:ENABLE_DIRECTOR
-define:ENABLE_LOCALIZATION
-define:ENABLE_SPRITES
-define:ENABLE_TERRAIN
-define:ENABLE_TILEMAP
-define:ENABLE_TIMELINE
-define:ENABLE_INPUT_SYSTEM
-define:TEXTCORE_FONT_ENGINE_1_5_OR_NEWER
-define:UNITY_POST_PROCESSING_STACK_V2
-define:ODIN_VALIDATOR
-define:ODIN_VALIDATOR_3_1
-define:ODIN_INSPECTOR
-define:ODIN_INSPECTOR_3
-define:ODIN_INSPECTOR_3_1
-define:MAGICACLOTH2
-define:TextMeshPro
-define:DOTWEEN
-define:UNITY_PIPELINE_URP
-define:LiveScriptReload_Enabled
-define:LiveScriptReload_IncludeInBuild_Enabled
-define:ENABLE_URP
-define:CSHARP_7_OR_LATER
-define:CSHARP_7_3_OR_NEWER
-r:"Assets/LiveScriptReload/Plugins/FastScriptReload/Plugins/Roslyn/2021+/System.Runtime.CompilerServices.Unsafe.dll"
-r:"Assets/NuGet/Editor/NugetForUnity.dll"
-r:"Assets/NuGet/Editor/NuGetForUnity.PluginAPI.dll"
-r:"Assets/Packages/MessagePack.3.1.3/lib/netstandard2.1/MessagePack.dll"
-r:"Assets/Packages/MessagePack.Annotations.3.1.3/lib/netstandard2.0/MessagePack.Annotations.dll"
-r:"Assets/Packages/Microsoft.NET.StringTools.17.11.4/lib/netstandard2.0/Microsoft.NET.StringTools.dll"
-r:"Assets/Packages/System.Collections.Immutable.8.0.0/lib/netstandard2.0/System.Collections.Immutable.dll"
-r:"Assets/Plugins/Demigiant/DemiLib/Core/DemiLib.dll"
-r:"Assets/Plugins/Demigiant/DemiLib/Core/Editor/DemiEditor.dll"
-r:"Assets/Plugins/Demigiant/DOTween/DOTween.dll"
-r:"Assets/Plugins/Demigiant/DOTween/Editor/DOTweenEditor.dll"
-r:"Assets/Plugins/Demigiant/DOTweenPro/DOTweenPro.dll"
-r:"Assets/Plugins/Demigiant/DOTweenPro/Editor/DOTweenProEditor.dll"
-r:"Assets/Plugins/Protobuf/Google.Protobuf.dll"
-r:"Assets/Plugins/Protobuf/NuGet.Frameworks.dll"
-r:"Assets/Plugins/Sirenix/Assemblies/Sirenix.OdinInspector.Attributes.dll"
-r:"Assets/Plugins/Sirenix/Assemblies/Sirenix.OdinInspector.Editor.dll"
-r:"Assets/Plugins/Sirenix/Assemblies/Sirenix.OdinValidator.Editor.dll"
-r:"Assets/Plugins/Sirenix/Assemblies/Sirenix.Serialization.Config.dll"
-r:"Assets/Plugins/Sirenix/Assemblies/Sirenix.Serialization.dll"
-r:"Assets/Plugins/Sirenix/Assemblies/Sirenix.Utilities.dll"
-r:"Assets/Plugins/Sirenix/Assemblies/Sirenix.Utilities.Editor.dll"
-r:"Assets/TEngine/Editor/Localization/Unity XCode/UnityEditor.iOS_I2Loc.Xcode.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEditor.Graphs.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.CoreModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.DeviceSimulatorModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.DiagnosticsModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.EditorToolbarModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.GraphViewModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.PresetsUIModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.QuickSearchModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.SceneTemplateModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.SceneViewModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.TextCoreFontEngineModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.TextCoreTextEngineModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.UIBuilderModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.UIElementsModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.UIElementsSamplesModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEditor.UnityConnectModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.AccessibilityModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.AIModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.AndroidJNIModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.AnimationModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.ARModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.AssetBundleModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.AudioModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.ClothModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.ClusterInputModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.ClusterRendererModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.ContentLoadModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.CoreModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.CrashReportingModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.DirectorModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.DSPGraphModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.GameCenterModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.GIModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.GridModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.HotReloadModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.ImageConversionModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.IMGUIModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.InputLegacyModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.InputModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.JSONSerializeModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.LocalizationModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.ParticleSystemModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.PerformanceReportingModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.Physics2DModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.PhysicsModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.ProfilerModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.PropertiesModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.ScreenCaptureModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.SharedInternalsModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.SpriteMaskModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.SpriteShapeModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.StreamingModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.SubstanceModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.SubsystemsModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.TerrainModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.TerrainPhysicsModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.TextCoreFontEngineModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.TextCoreTextEngineModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.TextRenderingModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.TilemapModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.TLSModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UIElementsModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UIModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UmbraModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityAnalyticsCommonModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityAnalyticsModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityConnectModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityCurlModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityTestProtocolModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestAssetBundleModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestAudioModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestTextureModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestWWWModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.VehiclesModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.VFXModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.VideoModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.VirtualTexturingModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.VRModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.WindModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Managed/UnityEngine/UnityEngine.XRModule.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/PlaybackEngines/AndroidPlayer/Unity.Android.Gradle.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/PlaybackEngines/AndroidPlayer/Unity.Android.GradleProject.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/PlaybackEngines/AndroidPlayer/Unity.Android.Types.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/Microsoft.Win32.Primitives.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/netstandard.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.AppContext.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Buffers.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Collections.Concurrent.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Collections.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Collections.NonGeneric.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Collections.Specialized.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ComponentModel.Annotations.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ComponentModel.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ComponentModel.EventBasedAsync.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ComponentModel.Primitives.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ComponentModel.TypeConverter.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Console.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Data.Common.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.Contracts.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.Debug.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.FileVersionInfo.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.Process.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.StackTrace.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.TextWriterTraceListener.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.Tools.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Diagnostics.TraceSource.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Drawing.Primitives.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Dynamic.Runtime.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Globalization.Calendars.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Globalization.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Globalization.Extensions.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.Compression.ZipFile.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.FileSystem.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.FileSystem.DriveInfo.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.FileSystem.Primitives.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.FileSystem.Watcher.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.IsolatedStorage.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.MemoryMappedFiles.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.Pipes.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.IO.UnmanagedMemoryStream.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Linq.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Linq.Expressions.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Linq.Parallel.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Linq.Queryable.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Memory.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Http.Rtc.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.NameResolution.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.NetworkInformation.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Ping.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Primitives.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Requests.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Security.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.Sockets.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.WebHeaderCollection.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.WebSockets.Client.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Net.WebSockets.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ObjectModel.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.DispatchProxy.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.Emit.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.Emit.ILGeneration.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.Emit.Lightweight.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.Extensions.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Reflection.Primitives.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Resources.Reader.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Resources.ResourceManager.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Resources.Writer.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.CompilerServices.VisualC.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Extensions.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Handles.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.InteropServices.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.InteropServices.RuntimeInformation.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.InteropServices.WindowsRuntime.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Numerics.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Serialization.Formatters.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Serialization.Json.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Serialization.Primitives.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Runtime.Serialization.Xml.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Claims.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Cryptography.Algorithms.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Cryptography.Csp.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Cryptography.Encoding.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Cryptography.Primitives.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Cryptography.X509Certificates.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.Principal.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Security.SecureString.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ServiceModel.Duplex.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ServiceModel.Http.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ServiceModel.NetTcp.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ServiceModel.Primitives.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ServiceModel.Security.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Text.Encoding.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Text.Encoding.Extensions.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Text.RegularExpressions.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Overlapped.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Tasks.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Tasks.Extensions.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Tasks.Parallel.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Thread.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.ThreadPool.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Threading.Timer.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.ValueTuple.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.ReaderWriter.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.XDocument.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.XmlDocument.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.XmlSerializer.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.XPath.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Facades/System.Xml.XPath.XDocument.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/Microsoft.CSharp.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/mscorlib.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.ComponentModel.Composition.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Core.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Data.DataSetExtensions.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Data.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Drawing.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.IO.Compression.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.IO.Compression.FileSystem.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Net.Http.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Numerics.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Numerics.Vectors.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Runtime.Serialization.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Transactions.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Xml.dll"
-r:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/UnityReferenceAssemblies/unity-4.8-api/System.Xml.Linq.dll"
-r:"Library/PackageCache/com.code-philosophy.hybridclr@59a3c3974a/Plugins/dnlib.dll"
-r:"Library/PackageCache/com.code-philosophy.hybridclr@59a3c3974a/Plugins/LZ4.dll"
-r:"Library/PackageCache/com.unity.collections@2.5.1/Unity.Collections.LowLevel.ILSupport/Unity.Collections.LowLevel.ILSupport.dll"
-r:"Library/PackageCache/com.unity.ext.nunit@2.0.3/net40/unity-custom/nunit.framework.dll"
-r:"Library/PackageCache/com.unity.nuget.mono-cecil@1.11.4/Mono.Cecil.dll"
-r:"Library/PackageCache/com.unity.nuget.newtonsoft-json@3.2.1/Runtime/Newtonsoft.Json.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Cinemachine.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/DG.Tweening.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/GameBase.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/GameCommon.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/GameProto.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/JUTPS.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/PhysicsTankMaker.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/TEngine.Runtime.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/UniTask.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Unity.AI.Navigation.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Unity.InputSystem.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Unity.TextMeshPro.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/UnityEditor.UI.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/UnityEngine.UI.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/YooAsset.ref.dll"
-analyzer:"Assets/Packages/MessagePackAnalyzer.3.1.3/analyzers/roslyn4.3/cs/MessagePack.Analyzers.CodeFixes.dll"
-analyzer:"Assets/Packages/MessagePackAnalyzer.3.1.3/analyzers/roslyn4.3/cs/MessagePack.SourceGenerator.dll"
-analyzer:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Tools/Unity.SourceGenerators/Unity.Properties.SourceGenerator.dll"
-analyzer:"D:/Softs/Unity Editor/2022.3.51f1c1/Editor/Data/Tools/Unity.SourceGenerators/Unity.SourceGenerators.dll"
"Assets/GameScripts/HotFix/GameLogic/Battle/AI/Behavior/RoleAttackBehavior.cs"
"Assets/GameScripts/HotFix/GameLogic/Battle/AI/Behavior/TankAttackBehavior.cs"
"Assets/GameScripts/HotFix/GameLogic/Battle/AI/Command/Command.cs"
"Assets/GameScripts/HotFix/GameLogic/Battle/AI/Command/CommandDispatcher.cs"
"Assets/GameScripts/HotFix/GameLogic/Battle/AI/Command/CommandManager.cs"
"Assets/GameScripts/HotFix/GameLogic/Battle/AI/Command/InstantCommandExecutor.cs"
"Assets/GameScripts/HotFix/GameLogic/Battle/AI/Command/PhaseCommandExecutor.cs"
"Assets/GameScripts/HotFix/GameLogic/Battle/AI/Command/ReactiveCommandExecutor.cs"
"Assets/GameScripts/HotFix/GameLogic/Battle/AI/Command/RushCommand.cs"
"Assets/GameScripts/HotFix/GameLogic/Battle/AI/CommandHandler/BumpCommandHandler.cs"
"Assets/GameScripts/HotFix/GameLogic/Battle/AI/CommandHandler/EjectionCmdHandler.cs"
"Assets/GameScripts/HotFix/GameLogic/Battle/AI/CommandHandler/IInstantCommandHandler.cs"
"Assets/GameScripts/HotFix/GameLogic/Battle/AI/CommandHandler/IPhaseCommandHandler.cs"
"Assets/GameScripts/HotFix/GameLogic/Battle/AI/CommandHandler/IReactiveCommandHandler.cs"
"Assets/GameScripts/HotFix/GameLogic/Battle/AI/CommandHandler/MoveToCmdHandler.cs"
"Assets/GameScripts/HotFix/GameLogic/Battle/AI/CommandHandler/ShootTargetCommandHandler.cs"
"Assets/GameScripts/HotFix/GameLogic/Battle/AI/CommandHandler/StopMoveCommandHandler.cs"
"Assets/GameScripts/HotFix/GameLogic/Battle/AI/CoroutineRunner.cs"
"Assets/GameScripts/HotFix/GameLogic/Battle/AI/Cover/CoverObject.cs"
"Assets/GameScripts/HotFix/GameLogic/Battle/AI/Cover/CoverPointManager.cs"
"Assets/GameScripts/HotFix/GameLogic/Battle/AI/Formation/BattleFormationController.cs"
"Assets/GameScripts/HotFix/GameLogic/Battle/AI/Node/AutoAttack/AutoAttackEnabledNode.cs"
"Assets/GameScripts/HotFix/GameLogic/Battle/AI/Node/AutoAttack/ChaseHateTargetNode.cs"
"Assets/GameScripts/HotFix/GameLogic/Battle/AI/Node/AutoAttack/FindNewHateTargetNode.cs"
"Assets/GameScripts/HotFix/GameLogic/Battle/AI/Node/AutoAttack/HandleCrowdingNode.cs"
"Assets/GameScripts/HotFix/GameLogic/Battle/AI/Node/AutoAttack/HandleVisionObstructionNode.cs"
"Assets/GameScripts/HotFix/GameLogic/Battle/AI/Node/AutoAttack/RepositioningNode.cs"
"Assets/GameScripts/HotFix/GameLogic/Battle/AI/Node/AutoAttack/ShootWithPhasesNode.cs"
"Assets/GameScripts/HotFix/GameLogic/Battle/AI/Node/BehaviorTreeNode.cs"
"Assets/GameScripts/HotFix/GameLogic/Battle/AI/Node/CollisionNode.cs"
"Assets/GameScripts/HotFix/GameLogic/Battle/AI/Node/CommandDispatchNode.cs"
"Assets/GameScripts/HotFix/GameLogic/Battle/AI/Node/GearChangeNode.cs"
"Assets/GameScripts/HotFix/GameLogic/Battle/AI/Node/JoystickMoveNode.cs"
"Assets/GameScripts/HotFix/GameLogic/Battle/AI/Node/MoveToTargetNode.cs"
"Assets/GameScripts/HotFix/GameLogic/Battle/AI/Node/PlayerCommand/CmdChargeNode.cs"
"Assets/GameScripts/HotFix/GameLogic/Battle/AI/Node/PlayerCommand/CmdFindCoverNode.cs"
"Assets/GameScripts/HotFix/GameLogic/Battle/AI/Node/PlayerCommand/CmdFollowMeNode.cs"
"Assets/GameScripts/HotFix/GameLogic/Battle/AI/Node/PlayerCommand/CmdForwardNode.cs"
"Assets/GameScripts/HotFix/GameLogic/Battle/AI/Node/PlayerCommand/CmdQuicklyDisperseNode.cs"
"Assets/GameScripts/HotFix/GameLogic/Battle/AI/Node/PlayerCommand/CmdRetreatNode.cs"
"Assets/GameScripts/HotFix/GameLogic/Battle/AI/Node/PlayerCommand/CmdStandingNode.cs"
"Assets/GameScripts/HotFix/GameLogic/Battle/AI/Node/PlayerCommand/IInteractionStrategy.cs"
"Assets/GameScripts/HotFix/GameLogic/Battle/AI/Node/PlayerCommand/InteractiveCommandNode.cs"
"Assets/GameScripts/HotFix/GameLogic/Battle/AI/Node/PlayerCommand/MoveBackNode.cs"
"Assets/GameScripts/HotFix/GameLogic/Battle/AI/Node/PlayerCommand/SelectHateTargetStrategy.cs"
"Assets/GameScripts/HotFix/GameLogic/Battle/AI/Node/PostureChangeNode.cs"
"Assets/GameScripts/HotFix/GameLogic/Battle/AI/Node/SelectorNode.cs"
"Assets/GameScripts/HotFix/GameLogic/Battle/AI/Node/SequenceNode.cs"
"Assets/GameScripts/HotFix/GameLogic/Battle/AI/Node/WaitUntilReachTargetNode.cs"
"Assets/GameScripts/HotFix/GameLogic/Battle/AI/TargetButtonHandler.cs"
"Assets/GameScripts/HotFix/GameLogic/Battle/AI/TargetingSystem.cs"
"Assets/GameScripts/HotFix/GameLogic/Battle/AI/TargetProjector.cs"
"Assets/GameScripts/HotFix/GameLogic/Battle/BattleAirStrikeBomb.cs"
"Assets/GameScripts/HotFix/GameLogic/Battle/BattleAirStrikePlane.cs"
"Assets/GameScripts/HotFix/GameLogic/Battle/BattleCamera.cs"
"Assets/GameScripts/HotFix/GameLogic/Battle/BattleCameraManager.cs"
"Assets/GameScripts/HotFix/GameLogic/Battle/BattleDefined.cs"
"Assets/GameScripts/HotFix/GameLogic/Battle/BattleEventTrigger.cs"
"Assets/GameScripts/HotFix/GameLogic/Battle/BattleLeaderMove.cs"
"Assets/GameScripts/HotFix/GameLogic/Battle/BattleMiniMapController.cs"
"Assets/GameScripts/HotFix/GameLogic/Battle/BattleSystem.cs"
"Assets/GameScripts/HotFix/GameLogic/Battle/BattleTickManager.cs"
"Assets/GameScripts/HotFix/GameLogic/Battle/BattleUnitClick.cs"
"Assets/GameScripts/HotFix/GameLogic/Battle/BattleWeaponBase.cs"
"Assets/GameScripts/HotFix/GameLogic/Battle/BattleWeaponFly.cs"
"Assets/GameScripts/HotFix/GameLogic/Battle/Camera/CommanderCamera.cs"
"Assets/GameScripts/HotFix/GameLogic/Battle/Context/BattleConstants.cs"
"Assets/GameScripts/HotFix/GameLogic/Battle/Context/BattleContext.cs"
"Assets/GameScripts/HotFix/GameLogic/Battle/Context/BlackboardKeys.cs"
"Assets/GameScripts/HotFix/GameLogic/Battle/Context/CommandTypeComparer.cs"
"Assets/GameScripts/HotFix/GameLogic/Battle/Context/TeamBlackboard.cs"
"Assets/GameScripts/HotFix/GameLogic/Battle/Context/TypedBlackboardBase.cs"
"Assets/GameScripts/HotFix/GameLogic/Battle/Context/UnitBlackboard.cs"
"Assets/GameScripts/HotFix/GameLogic/Battle/Perception/PerceptionManager.cs"
"Assets/GameScripts/HotFix/GameLogic/Battle/Perception/TeamPerceptionSystem.cs"
"Assets/GameScripts/HotFix/GameLogic/Battle/Perception/UnitPerceptionSystem.cs"
"Assets/GameScripts/HotFix/GameLogic/Battle/PoolManager.cs"
"Assets/GameScripts/HotFix/GameLogic/Battle/TargetSelection/Filters/AllyOnlySelection.cs"
"Assets/GameScripts/HotFix/GameLogic/Battle/TargetSelection/Filters/EnemyOnlySelection.cs"
"Assets/GameScripts/HotFix/GameLogic/Battle/TargetSelection/Highlight/Highlightable.cs"
"Assets/GameScripts/HotFix/GameLogic/Battle/TargetSelection/ITargetSelectionFilter.cs"
"Assets/GameScripts/HotFix/GameLogic/Battle/TargetSelection/TargetSelectionContext.cs"
"Assets/GameScripts/HotFix/GameLogic/Battle/TargetSelection/TargetSelectionSystem.cs"
"Assets/GameScripts/HotFix/GameLogic/Battle/TargetSelection/UI/TargetSelectionUI.cs"
"Assets/GameScripts/HotFix/GameLogic/Battle/TeamCommandExecutor.cs"
"Assets/GameScripts/HotFix/GameLogic/Battle/UIBattleUnitHealthBar.cs"
"Assets/GameScripts/HotFix/GameLogic/Battle/Unit/BaseController.cs"
"Assets/GameScripts/HotFix/GameLogic/Battle/Unit/RoleController.cs"
"Assets/GameScripts/HotFix/GameLogic/Battle/Unit/RoleMoveComponent.cs"
"Assets/GameScripts/HotFix/GameLogic/Battle/Unit/TankController.cs"
"Assets/GameScripts/HotFix/GameLogic/Battle/Unit/UniOperationAi.cs"
"Assets/GameScripts/HotFix/GameLogic/Battle/Unit/UniOperationBase.cs"
"Assets/GameScripts/HotFix/GameLogic/Battle/Unit/UnitActor.cs"
"Assets/GameScripts/HotFix/GameLogic/Battle/Unit/UnitAI.cs"
"Assets/GameScripts/HotFix/GameLogic/Common/BindPropCount.cs"
"Assets/GameScripts/HotFix/GameLogic/Common/CodeTypes/Attribute/BaseAttribute.cs"
"Assets/GameScripts/HotFix/GameLogic/Common/CodeTypes/CodeTypes.cs"
"Assets/GameScripts/HotFix/GameLogic/Common/CodeTypes/UnOrderMultiMapSet.cs"
"Assets/GameScripts/HotFix/GameLogic/Common/Common3DNodeView.cs"
"Assets/GameScripts/HotFix/GameLogic/Common/DataBind/DataBindingManager.cs"
"Assets/GameScripts/HotFix/GameLogic/Common/Extend/CustomToggle.cs"
"Assets/GameScripts/HotFix/GameLogic/Common/ScrollPageController.cs"
"Assets/GameScripts/HotFix/GameLogic/Common/TFTRadarChart.cs"
"Assets/GameScripts/HotFix/GameLogic/Common/ViewUtils.cs"
"Assets/GameScripts/HotFix/GameLogic/Common/WarpperObject/SRoleWrapper.cs"
"Assets/GameScripts/HotFix/GameLogic/Common/ZoomAndDragImage.cs"
"Assets/GameScripts/HotFix/GameLogic/Event/EventInterfaceHelper.cs"
"Assets/GameScripts/HotFix/GameLogic/Event/EventInterfaceImpAttribute.cs"
"Assets/GameScripts/HotFix/GameLogic/Event/RegisterEventInterface_Logic.cs"
"Assets/GameScripts/HotFix/GameLogic/Event/RegisterEventInterface_UI.cs"
"Assets/GameScripts/HotFix/GameLogic/GameApp.cs"
"Assets/GameScripts/HotFix/GameLogic/GameApp_RegisterSystem.cs"
"Assets/GameScripts/HotFix/GameLogic/Hall/HallSystem.cs"
"Assets/GameScripts/HotFix/GameLogic/OnEnterGameAppProcedure.cs"
"Assets/GameScripts/HotFix/GameLogic/TpsTest/Aoe/AoeTargetingManager.cs"
"Assets/GameScripts/HotFix/GameLogic/TpsTest/AutoDestroyOnImpact.cs"
"Assets/GameScripts/HotFix/GameLogic/TpsTest/BehaviorTreeDebugger.cs"
"Assets/GameScripts/HotFix/GameLogic/TpsTest/CameraDirectionAgentMover.cs"
"Assets/GameScripts/HotFix/GameLogic/TpsTest/CharacterSwitchSystem/CharacterIndicator.cs"
"Assets/GameScripts/HotFix/GameLogic/TpsTest/CharacterSwitchSystem/CharacterManager.cs"
"Assets/GameScripts/HotFix/GameLogic/TpsTest/CharacterSwitchSystem/CharacterSwitchInput.cs"
"Assets/GameScripts/HotFix/GameLogic/TpsTest/CharacterSwitchSystem/CharacterSwitchUI.cs"
"Assets/GameScripts/HotFix/GameLogic/TpsTest/CharacterSwitchSystem/MultiCharacterSceneSetup.cs"
"Assets/GameScripts/HotFix/GameLogic/TpsTest/CharacterSwitchSystem/RoleCrouchTest.cs"
"Assets/GameScripts/HotFix/GameLogic/TpsTest/CharacterSwitchSystem/SetupCustomInputs.cs"
"Assets/GameScripts/HotFix/GameLogic/TpsTest/CustomizeMove.cs"
"Assets/GameScripts/HotFix/GameLogic/TpsTest/JoyCoin/HoldToJoystickController.cs"
"Assets/GameScripts/HotFix/GameLogic/TpsTest/JoyCoin/JoyMove.cs"
"Assets/GameScripts/HotFix/GameLogic/TpsTest/JoyCoin/JoystickController.cs"
"Assets/GameScripts/HotFix/GameLogic/TpsTest/JoyCoin/PreviewJoyController.cs"
"Assets/GameScripts/HotFix/GameLogic/TpsTest/JoyCoin/TouchDragHandler.cs"
"Assets/GameScripts/HotFix/GameLogic/TpsTest/PrefabShooter.cs"
"Assets/GameScripts/HotFix/GameLogic/TpsTest/ShootTest.cs"
"Assets/GameScripts/HotFix/GameLogic/TpsTest/SupportPreviewMover.cs"
"Assets/GameScripts/HotFix/GameLogic/TpsTest/TankAttackTest.cs"
"Assets/GameScripts/HotFix/GameLogic/TpsTest/TestMoveUI.cs"
"Assets/GameScripts/HotFix/GameLogic/TpsTest/UI/UIManager.cs"
"Assets/GameScripts/HotFix/GameLogic/UI/DialogCurrencyTip.cs"
"Assets/GameScripts/HotFix/GameLogic/UI/DialogItemAwardProp.cs"
"Assets/GameScripts/HotFix/GameLogic/UI/DialogTipsAward1.cs"
"Assets/GameScripts/HotFix/GameLogic/UI/DialogTipsCallback.cs"
"Assets/GameScripts/HotFix/GameLogic/UI/DialogTipsText.cs"
"Assets/GameScripts/HotFix/GameLogic/UI/UIAchievement.cs"
"Assets/GameScripts/HotFix/GameLogic/UI/UIAchiItem.cs"
"Assets/GameScripts/HotFix/GameLogic/UI/UIAchiRewardItem.cs"
"Assets/GameScripts/HotFix/GameLogic/UI/UIActivityCenter.cs"
"Assets/GameScripts/HotFix/GameLogic/UI/UIActivityListItem.cs"
"Assets/GameScripts/HotFix/GameLogic/UI/UIActivityNotice.cs"
"Assets/GameScripts/HotFix/GameLogic/UI/UIAlarm.cs"
"Assets/GameScripts/HotFix/GameLogic/UI/UIBarrack.cs"
"Assets/GameScripts/HotFix/GameLogic/UI/UIBarrackItem.cs"
"Assets/GameScripts/HotFix/GameLogic/UI/UIBatPrepItem.cs"
"Assets/GameScripts/HotFix/GameLogic/UI/UIBattleChoice.cs"
"Assets/GameScripts/HotFix/GameLogic/UI/UIBattleCmdItem.cs"
"Assets/GameScripts/HotFix/GameLogic/UI/UIBattleHeadItem.cs"
"Assets/GameScripts/HotFix/GameLogic/UI/UIBattleLeaderItem.cs"
"Assets/GameScripts/HotFix/GameLogic/UI/UIBattleMainPage.cs"
"Assets/GameScripts/HotFix/GameLogic/UI/UIBattlePrepare.cs"
"Assets/GameScripts/HotFix/GameLogic/UI/UIBattleRoleInfoDialog.cs"
"Assets/GameScripts/HotFix/GameLogic/UI/UIBattleStarInfoDialog.cs"
"Assets/GameScripts/HotFix/GameLogic/UI/UIBattleStatePropDialog.cs"
"Assets/GameScripts/HotFix/GameLogic/UI/UIBattleStatePropInfo.cs"
"Assets/GameScripts/HotFix/GameLogic/UI/UIBattleStatePropItem.cs"
"Assets/GameScripts/HotFix/GameLogic/UI/UIBattleStateRTS.cs"
"Assets/GameScripts/HotFix/GameLogic/UI/UIBattleStateTPS.cs"
"Assets/GameScripts/HotFix/GameLogic/UI/UIBattleTargetSelect.cs"
"Assets/GameScripts/HotFix/GameLogic/UI/UIBattleTipDialog.cs"
"Assets/GameScripts/HotFix/GameLogic/UI/UICampaign.cs"
"Assets/GameScripts/HotFix/GameLogic/UI/UICampaignItem.cs"
"Assets/GameScripts/HotFix/GameLogic/UI/UIChapter.cs"
"Assets/GameScripts/HotFix/GameLogic/UI/UIChapterLevelInfo.cs"
"Assets/GameScripts/HotFix/GameLogic/UI/UIChapterLevelItem.cs"
"Assets/GameScripts/HotFix/GameLogic/UI/UIChatCommentItem.cs"
"Assets/GameScripts/HotFix/GameLogic/UI/UIChatContactItem.cs"
"Assets/GameScripts/HotFix/GameLogic/UI/UIChatDialog.cs"
"Assets/GameScripts/HotFix/GameLogic/UI/UIChatList.cs"
"Assets/GameScripts/HotFix/GameLogic/UI/UIChatMessageInfo.cs"
"Assets/GameScripts/HotFix/GameLogic/UI/UIChatMoment.cs"
"Assets/GameScripts/HotFix/GameLogic/UI/UIChatMomentImageItem.cs"
"Assets/GameScripts/HotFix/GameLogic/UI/UIChatMomentInfo.cs"
"Assets/GameScripts/HotFix/GameLogic/UI/UIChatMomentItem.cs"
"Assets/GameScripts/HotFix/GameLogic/UI/UIChatMomentLargeImgItem.cs"
"Assets/GameScripts/HotFix/GameLogic/UI/UIChatMsgRoleItem.cs"
"Assets/GameScripts/HotFix/GameLogic/UI/UIChatMsgSelfItem.cs"
"Assets/GameScripts/HotFix/GameLogic/UI/UICulArmor.cs"
"Assets/GameScripts/HotFix/GameLogic/UI/UICulArmorInfo.cs"
"Assets/GameScripts/HotFix/GameLogic/UI/UICulAttrInfo.cs"
"Assets/GameScripts/HotFix/GameLogic/UI/UICulAvater.cs"
"Assets/GameScripts/HotFix/GameLogic/UI/UICulBondInfo.cs"
"Assets/GameScripts/HotFix/GameLogic/UI/UICulBoneInfoItem.cs"
"Assets/GameScripts/HotFix/GameLogic/UI/UICulBulletAttrDialog.cs"
"Assets/GameScripts/HotFix/GameLogic/UI/UICulBulletItem.cs"
"Assets/GameScripts/HotFix/GameLogic/UI/UICulEquipmentDialog.cs"
"Assets/GameScripts/HotFix/GameLogic/UI/UICulEquipmentItem.cs"
"Assets/GameScripts/HotFix/GameLogic/UI/UICulGrowUp.cs"
"Assets/GameScripts/HotFix/GameLogic/UI/UICulMainWeaponDialog.cs"
"Assets/GameScripts/HotFix/GameLogic/UI/UICulReForm.cs"
"Assets/GameScripts/HotFix/GameLogic/UI/UICulReformAttrItem.cs"
"Assets/GameScripts/HotFix/GameLogic/UI/UICulReformItemInfo.cs"
"Assets/GameScripts/HotFix/GameLogic/UI/UICulReformMItem.cs"
"Assets/GameScripts/HotFix/GameLogic/UI/UICulReformPItem.cs"
"Assets/GameScripts/HotFix/GameLogic/UI/UICulSkillDialog.cs"
"Assets/GameScripts/HotFix/GameLogic/UI/UICulSkillItem.cs"
"Assets/GameScripts/HotFix/GameLogic/UI/UICulSkinInfo.cs"
"Assets/GameScripts/HotFix/GameLogic/UI/UICulSkinItem.cs"
"Assets/GameScripts/HotFix/GameLogic/UI/UICultivate.cs"
"Assets/GameScripts/HotFix/GameLogic/UI/UIDisAvaterItem.cs"
"Assets/GameScripts/HotFix/GameLogic/UI/UIDispatch.cs"
"Assets/GameScripts/HotFix/GameLogic/UI/UIDispatch2.cs"
"Assets/GameScripts/HotFix/GameLogic/UI/UIDispatchItem.cs"
"Assets/GameScripts/HotFix/GameLogic/UI/UIDispatchItemInfo.cs"
"Assets/GameScripts/HotFix/GameLogic/UI/UIDisRewardItem.cs"
"Assets/GameScripts/HotFix/GameLogic/UI/UIEmail.cs"
"Assets/GameScripts/HotFix/GameLogic/UI/UIEmailItem.cs"
"Assets/GameScripts/HotFix/GameLogic/UI/UIEnemyIndicator.cs"
"Assets/GameScripts/HotFix/GameLogic/UI/UIFormation.cs"
"Assets/GameScripts/HotFix/GameLogic/UI/UIFormationAvaterItem.cs"
"Assets/GameScripts/HotFix/GameLogic/UI/UIFormationQueueItem.cs"
"Assets/GameScripts/HotFix/GameLogic/UI/UIGameGuide.cs"
"Assets/GameScripts/HotFix/GameLogic/UI/UIGameGuideInfoItem.cs"
"Assets/GameScripts/HotFix/GameLogic/UI/UIGameGuideItem.cs"
"Assets/GameScripts/HotFix/GameLogic/UI/UIGameSetting.cs"
"Assets/GameScripts/HotFix/GameLogic/UI/UIHall.cs"
"Assets/GameScripts/HotFix/GameLogic/UI/UIHallKbnItem.cs"
"Assets/GameScripts/HotFix/GameLogic/UI/UIHomeLand.cs"
"Assets/GameScripts/HotFix/GameLogic/UI/UILabAvaterItem.cs"
"Assets/GameScripts/HotFix/GameLogic/UI/UILabMainPropItem.cs"
"Assets/GameScripts/HotFix/GameLogic/UI/UILaboratory.cs"
"Assets/GameScripts/HotFix/GameLogic/UI/UILabPItem.cs"
"Assets/GameScripts/HotFix/GameLogic/UI/UILabSecondPropItem.cs"
"Assets/GameScripts/HotFix/GameLogic/UI/UILabSub1.cs"
"Assets/GameScripts/HotFix/GameLogic/UI/UILabSub2.cs"
"Assets/GameScripts/HotFix/GameLogic/UI/UILabSub3.cs"
"Assets/GameScripts/HotFix/GameLogic/UI/UILabSub4.cs"
"Assets/GameScripts/HotFix/GameLogic/UI/UILoading.cs"
"Assets/GameScripts/HotFix/GameLogic/UI/UILogin.cs"
"Assets/GameScripts/HotFix/GameLogic/UI/UINoteBook.cs"
"Assets/GameScripts/HotFix/GameLogic/UI/UIPassport.cs"
"Assets/GameScripts/HotFix/GameLogic/UI/UIPassportItem.cs"
"Assets/GameScripts/HotFix/GameLogic/UI/UIPInfoHallImgItem.cs"
"Assets/GameScripts/HotFix/GameLogic/UI/UIPInfoSecretaryItem.cs"
"Assets/GameScripts/HotFix/GameLogic/UI/UIPInfoSkinItem.cs"
"Assets/GameScripts/HotFix/GameLogic/UI/UIPlayerInfo.cs"
"Assets/GameScripts/HotFix/GameLogic/UI/UIPlotDialogue.cs"
"Assets/GameScripts/HotFix/GameLogic/UI/UIRoleSelect.cs"
"Assets/GameScripts/HotFix/GameLogic/UI/UIRoleSelectItem.cs"
"Assets/GameScripts/HotFix/GameLogic/UI/UIShop.cs"
"Assets/GameScripts/HotFix/GameLogic/UI/UIShopMaterialItem.cs"
"Assets/GameScripts/HotFix/GameLogic/UI/UIShopMInfoItem.cs"
"Assets/GameScripts/HotFix/GameLogic/UI/UIShopRechargeItem.cs"
"Assets/GameScripts/HotFix/GameLogic/UI/UIShopSkinHeadItem.cs"
"Assets/GameScripts/HotFix/GameLogic/UI/UIShopSkinInfo.cs"
"Assets/GameScripts/HotFix/GameLogic/UI/UIShopSkinSeriesItem.cs"
"Assets/GameScripts/HotFix/GameLogic/UI/UIShopSub1.cs"
"Assets/GameScripts/HotFix/GameLogic/UI/UIShopSub2.cs"
"Assets/GameScripts/HotFix/GameLogic/UI/UIShopSub3.cs"
"Assets/GameScripts/HotFix/GameLogic/UI/UIShopSub4.cs"
"Assets/GameScripts/HotFix/GameLogic/UI/UIShopSubMain.cs"
"Assets/GameScripts/HotFix/GameLogic/UI/UIShopTradeMInfoItem.cs"
"Assets/GameScripts/HotFix/GameLogic/UI/UIShopTradeMItem.cs"
"Assets/GameScripts/HotFix/GameLogic/UI/UITask.cs"
"Assets/GameScripts/HotFix/GameLogic/UI/UITaskDailyItem.cs"
"Assets/GameScripts/HotFix/GameLogic/UI/UITaskMainItem.cs"
"Assets/GameScripts/HotFix/GameLogic/UI/UITaskMainTaskInfo.cs"
"Assets/GameScripts/HotFix/GameLogic/UI/UITaskSub1.cs"
"Assets/GameScripts/HotFix/GameLogic/UI/UITaskSub2.cs"
"Assets/GameScripts/HotFix/GameLogic/UI/UIVictory1.cs"
"Assets/GameScripts/HotFix/GameLogic/UI/UIVictory2.cs"
"Assets/GameScripts/HotFix/GameLogic/UI/UIVictory3.cs"
"Assets/GameScripts/HotFix/GameLogic/UI/UIVictory3StartItem.cs"
"Assets/GameScripts/HotFix/GameLogic/UI/UIViewMode.cs"
"Assets/GameScripts/HotFix/GameLogic/UI/UIVitcoryRewardItem.cs"
"Assets/GameScripts/HotFix/GameLogic/UI/UIWaiting.cs"
"Assets/GameScripts/HotFix/GameLogic/UI/UIWareHouse.cs"
"Assets/GameScripts/HotFix/GameLogic/UI/UIWareHouseInfo.cs"
"Assets/GameScripts/HotFix/GameLogic/UI/UIWareHouseProp.cs"
-langversion:9.0
/deterministic
/optimize-
/debug:portable
/nologo
/RuntimeMetadataVersion:v4.0.30319
/nowarn:0169
/nowarn:0649
/nowarn:0282
/nowarn:1701
/nowarn:1702
/utf8output
/preferreduilang:en-US
/additionalfile:"Library/Bee/artifacts/1900b0aEDbg.dag/GameLogic.UnityAdditionalFile.txt"